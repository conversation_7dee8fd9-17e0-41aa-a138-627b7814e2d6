import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { Queue } from 'bull';
import { QueueName, EmailMarketingJobName } from '@shared/queue/queue.constants';
import { UserCampaignRepository } from '../repositories/user-campaign.repository';
import { UserCampaignHistoryRepository } from '../repositories/user-campaign-history.repository';
import { UserAudienceRepository } from '../repositories/user-audience.repository';

import { UserSegmentService } from './user-segment.service';
import { UserTemplateEmailRepository } from '../repositories/user-template-email.repository';
import { EmailServerConfigurationUserService } from '@modules/integration/user/services/email-server-configuration-user.service';
import { CreateEmailCampaignDto, CreateEmailCampaignWithTemplateDto, CreateEmailCampaignWithTemplateResponseDto, EmailMarketingJobDto, BatchEmailMarketingJobDto, EmailRecipientDto, CreateEmailCampaignResponseDto, RecentCampaignsResponseDto, RecentCampaignDto, EmailCampaignOverviewResponseDto } from '../dto/email-campaign';
import { UserCampaign, UserAudience, UserCampaignHistory } from '../entities';
import { SendStatus } from '../dto/campaign';
import { CampaignStatus } from '@/modules/marketing/enums/campaign-status.enum';
import { Transactional } from 'typeorm-transactional';
import { In, Like, FindManyOptions, Between, FindOptionsWhere } from 'typeorm';
import { QueryDto } from '@common/dto';
import { InjectQueue } from '@nestjs/bullmq';
import { EmailCampaignItemDto } from '../dto/email-campaign/email-campaign-list-response.dto';
import { EmailCampaignQueryDto } from '../dto/email-campaign/email-campaign-query.dto';
import { PaginatedResult } from '@/common/response';
import { CampaignComparisonDto, CampaignPerformanceItemDto, CampaignPerformanceListDto, OverviewDashboardDto, PerformanceMetricsDto, TrendChartDto, TrendsQueryDto } from '../dto/email-campaign/email-reports.dto';
import { BulkDeleteResponseDto } from '@/modules/marketing/common/dto';

/**
 * Service xử lý email marketing với queue integration
 */
@Injectable()
export class EmailMarketingService {
  private readonly logger = new Logger(EmailMarketingService.name);

  constructor(
    @InjectQueue(QueueName.EMAIL_MARKETING)
    private emailMarketingQueue: Queue,
    private readonly userCampaignRepository: UserCampaignRepository,
    private readonly userCampaignHistoryRepository: UserCampaignHistoryRepository,
    private readonly userAudienceRepository: UserAudienceRepository,
    private readonly userSegmentService: UserSegmentService,
    private readonly userTemplateEmailRepository: UserTemplateEmailRepository,
    private readonly emailServerConfigurationUserService: EmailServerConfigurationUserService,
  ) {}

  /**
   * Tạo email campaign và đẩy jobs vào queue
   * @param userId ID của người dùng
   * @param createDto Dữ liệu tạo campaign
   * @returns Thông tin campaign và jobs đã tạo
   */
  @Transactional()
  async createEmailCampaign(userId: number, createDto: CreateEmailCampaignDto): Promise<CreateEmailCampaignResponseDto> {
    this.logger.log(`Tạo email campaign cho user ${userId}: ${createDto.title}`);

    // Validate input
    if (!createDto.segmentId && (!createDto.audienceIds || createDto.audienceIds.length === 0)) {
      throw new BadRequestException('Phải chọn segment hoặc danh sách audience để gửi email');
    }

    // Tạo campaign trong database
    const campaign = new UserCampaign();
    campaign.userId = userId;
    campaign.title = createDto.title;
    campaign.description = createDto.description || '';
    campaign.platform = 'email';
    campaign.subject = createDto.subject;
    campaign.content = createDto.content;
    campaign.server = createDto.server;
    campaign.scheduledAt = createDto.scheduledAt || 0;
    campaign.segmentId = createDto.segmentId || null;
    campaign.audienceIds = createDto.audienceIds || null;
    campaign.status = createDto.scheduledAt ? CampaignStatus.SCHEDULED : CampaignStatus.SENDING;
    campaign.createdAt = Math.floor(Date.now() / 1000);
    campaign.updatedAt = Math.floor(Date.now() / 1000);

    const savedCampaign = await this.userCampaignRepository.save(campaign);
    this.logger.log(`Campaign đã được lưu với ID: ${savedCampaign.id}`);

    // Lấy danh sách audience
    const audiences = await this.getAudiencesForCampaign(userId, savedCampaign);
    this.logger.log(`Tìm thấy ${audiences.length} audience cho campaign`);

    if (audiences.length === 0) {
      throw new BadRequestException('Không tìm thấy audience nào để gửi email');
    }

    // Tạo batch job cho queue (không có template ID cho method này, dùng 0)
    const jobIds = await this.createEmailJobs(savedCampaign, audiences, 0);

    // Lưu job IDs vào campaign để có thể hủy sau này
    savedCampaign.jobIds = jobIds;
    await this.userCampaignRepository.save(savedCampaign);

    this.logger.log(`Đã tạo batch job cho campaign ${savedCampaign.id} với ${audiences.length} recipients`);

    return {
      campaignId: savedCampaign.id,
      jobCount: jobIds.length, // Sẽ luôn là 1 vì chỉ có 1 batch job
      jobIds,
      scheduledAt: savedCampaign.scheduledAt || undefined,
      status: savedCampaign.status,
    };
  }

  /**
   * Lấy danh sách audience cho campaign
   * @param userId ID của người dùng
   * @param campaign Campaign
   * @returns Danh sách audience
   */
  private async getAudiencesForCampaign(userId: number, campaign: UserCampaign): Promise<UserAudience[]> {
    let audiences: UserAudience[] = [];

    // Nếu có segmentId, lấy audience từ segment
    if (campaign.segmentId) {
      this.logger.log(`Lấy audience từ segment ${campaign.segmentId}`);
      const segment = await this.userSegmentService.findOne(userId, campaign.segmentId);
      if (!segment) {
        throw new NotFoundException(`Segment với ID ${campaign.segmentId} không tồn tại`);
      }
      // TODO: Implement getAudiencesInSegment method
      // audiences = await this.userSegmentService.getAudiencesInSegment(userId, segment);
      
      // Tạm thời lấy tất cả audience của user (cần implement logic segment sau)
      audiences = await this.userAudienceRepository.find({ where: { userId } });
    }

    // Nếu có audienceIds, lấy audience theo ID
    if (campaign.audienceIds && campaign.audienceIds.length > 0) {
      this.logger.log(`Lấy audience theo IDs: ${campaign.audienceIds.join(', ')}`);
      audiences = await this.userAudienceRepository.find({
        where: { id: In(campaign.audienceIds) }
      });
      // Filter chỉ lấy audience của user hiện tại
      audiences = audiences.filter(audience => audience.userId === userId);
    }

    // Lọc chỉ lấy audience có email
    audiences = audiences.filter(audience => audience.email && audience.email.trim() !== '');

    return audiences;
  }

  /**
   * Tạo batch email marketing job và đẩy vào queue
   * @param campaign Campaign
   * @param audiences Danh sách audience
   * @param templateId ID của template email
   * @param templateVariables Template variables từ campaign (optional)
   * @returns Danh sách job IDs (chỉ có 1 job ID cho batch)
   */
  private async createEmailJobs(campaign: UserCampaign, audiences: UserAudience[], templateId: number, templateVariables?: Record<string, any>): Promise<string[]> {
    const now = Date.now();

    // Tạo danh sách recipients cho batch job (chỉ cần audienceId và email)
    const recipients: EmailRecipientDto[] = audiences.map(audience => ({
      audienceId: audience.id,
      email: audience.email,
    }));

    // Tạo batch job data
    const batchJobData: BatchEmailMarketingJobDto = {
      campaignId: campaign.id,
      templateId, // Worker sẽ lấy subject/content từ templateId
      templateVariables: templateVariables || {}, // Template variables áp dụng cho tất cả recipients
      recipients,
      server: campaign.server,
      createdAt: now,
    };

    // Tính delay nếu có scheduledAt
    const delay = campaign.scheduledAt ? Math.max(0, campaign.scheduledAt * 1000 - now) : 0;

    // Thêm batch job vào queue
    const job = await this.emailMarketingQueue.add(EmailMarketingJobName.SEND_BATCH_EMAIL, batchJobData, {
      delay,
      attempts: 3,
      backoff: {
        type: 'exponential',
        delay: 2000,
      },
      removeOnComplete: 100,
      removeOnFail: 50,
    });

    return [job.id.toString()];
  }



  /**
   * Kiểm tra trạng thái queue
   * @returns Thông tin trạng thái queue
   */
  async getQueueStatus() {
    const waiting = await this.emailMarketingQueue.getWaiting();
    const active = await this.emailMarketingQueue.getActive();
    const completed = await this.emailMarketingQueue.getCompleted();
    const failed = await this.emailMarketingQueue.getFailed();

    return {
      waiting: waiting.length,
      active: active.length,
      completed: completed.length,
      failed: failed.length,
    };
  }

  /**
   * Lấy danh sách chiến dịch email gần đây có phân trang
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn với phân trang
   * @returns Danh sách chiến dịch gần đây có phân trang
   */
  async getRecentCampaignsPaginated(userId: number, queryDto: EmailCampaignQueryDto): Promise<PaginatedResult<RecentCampaignDto>> {
    this.logger.log(`Lấy danh sách chiến dịch email có phân trang cho user ${userId}`);

    const { page = 1, limit = 10, status, title, search, sortBy = 'createdAt', sortDirection = 'DESC' } = queryDto;

    // Tạo điều kiện where
    const where: FindOptionsWhere<UserCampaign> = {
      userId,
      platform: 'email' // Chỉ lấy email campaigns
    };

    // Thêm filter theo status nếu có
    if (status) {
      where.status = status;
    }

    // Thêm filter theo title hoặc search nếu có
    if (title || search) {
      const searchTerm = title || search;
      where.title = Like(`%${searchTerm}%`);
    }

    // Tính offset cho phân trang
    const offset = (page - 1) * limit;

    // Đếm tổng số campaign
    const totalItems = await this.userCampaignRepository.count({ where });

    // Lấy danh sách campaign với phân trang
    const campaigns = await this.userCampaignRepository.find({
      where,
      order: { [sortBy]: sortDirection },
      skip: offset,
      take: limit,
    });

    const recentCampaigns: RecentCampaignDto[] = [];

    for (const campaign of campaigns) {
      // Đếm tổng số người nhận (audience) cho campaign này
      const totalRecipients = await this.userCampaignHistoryRepository.count({
        where: { campaignId: campaign.id },
      });

      // Đếm số tin nhắn đã gửi thành công (SENT, DELIVERED, OPENED, CLICKED)
      const sentCount = await this.userCampaignHistoryRepository.count({
        where: {
          campaignId: campaign.id,
          status: In([SendStatus.SENT, SendStatus.DELIVERED, SendStatus.OPENED, SendStatus.CLICKED])
        },
      });

      // Đếm số tin nhắn đã mở (OPENED, CLICKED)
      const openedCount = await this.userCampaignHistoryRepository.count({
        where: {
          campaignId: campaign.id,
          status: In([SendStatus.OPENED, SendStatus.CLICKED])
        },
      });

      // Đếm số tin nhắn đã click
      const clickedCount = await this.userCampaignHistoryRepository.count({
        where: {
          campaignId: campaign.id,
          status: SendStatus.CLICKED
        },
      });

      // Tính tỷ lệ
      const sentRate = totalRecipients > 0 ? Math.round((sentCount / totalRecipients) * 100 * 10) / 10 : 0;
      const openRate = sentCount > 0 ? Math.round((openedCount / sentCount) * 100 * 10) / 10 : 0;
      const clickRate = sentCount > 0 ? Math.round((clickedCount / sentCount) * 100 * 10) / 10 : 0;

      // Xác định thời gian chạy (ưu tiên scheduledAt, nếu không có thì dùng createdAt)
      const runAt = campaign.scheduledAt && campaign.scheduledAt > 0 ? campaign.scheduledAt : campaign.createdAt;

      const recentCampaign: RecentCampaignDto = {
        id: campaign.id,
        name: campaign.title,
        totalRecipients,
        status: campaign.status,
        runAt,
        sentRate: totalRecipients > 0 ? sentRate : undefined,
        openRate: sentCount > 0 ? openRate : undefined,
        clickRate: sentCount > 0 ? clickRate : undefined,
      };

      recentCampaigns.push(recentCampaign);
    }

    return {
      items: recentCampaigns,
      meta: {
        totalItems,
        itemCount: recentCampaigns.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Lấy danh sách chiến dịch email gần đây
   * @param userId ID của người dùng
   * @param limit Số lượng chiến dịch cần lấy (mặc định 10)
   * @returns Danh sách chiến dịch gần đây
   */
  async getRecentCampaigns(userId: number, limit: number = 10): Promise<RecentCampaignsResponseDto> {
    this.logger.log(`Lấy ${limit} chiến dịch gần đây cho user ${userId}`);

    // Lấy danh sách campaign của user, sắp xếp theo thời gian tạo giảm dần
    const campaigns = await this.userCampaignRepository.find({
      where: {
        userId,
        platform: 'email' // Chỉ lấy email campaigns
      },
      order: { createdAt: 'DESC' },
      take: limit,
    });

    const recentCampaigns: RecentCampaignDto[] = [];

    // Tính toán thống kê cho từng campaign
    for (const campaign of campaigns) {
      const campaignHistory = await this.userCampaignHistoryRepository.find({
        where: { campaignId: campaign.id },
      });

      const totalRecipients = campaignHistory.length;

      // Tính số lượng đã gửi (SENT, DELIVERED, OPENED, CLICKED)
      const sentCount = campaignHistory.filter(h =>
        h.status === SendStatus.SENT ||
        h.status === SendStatus.DELIVERED ||
        h.status === SendStatus.OPENED ||
        h.status === SendStatus.CLICKED
      ).length;

      // Tính số lượng đã mở (OPENED, CLICKED)
      const openedCount = campaignHistory.filter(h =>
        h.status === SendStatus.OPENED ||
        h.status === SendStatus.CLICKED
      ).length;

      // Tính số lượng đã click
      const clickedCount = campaignHistory.filter(h =>
        h.status === SendStatus.CLICKED
      ).length;

      // Tính tỷ lệ
      const sentRate = totalRecipients > 0 ? Math.round((sentCount / totalRecipients) * 100 * 10) / 10 : 0;
      const openRate = sentCount > 0 ? Math.round((openedCount / sentCount) * 100 * 10) / 10 : 0;
      const clickRate = sentCount > 0 ? Math.round((clickedCount / sentCount) * 100 * 10) / 10 : 0;

      // Xác định thời gian chạy (ưu tiên scheduledAt, nếu không có thì dùng createdAt)
      const runAt = campaign.scheduledAt && campaign.scheduledAt > 0 ? campaign.scheduledAt : campaign.createdAt;

      const recentCampaign: RecentCampaignDto = {
        id: campaign.id,
        name: campaign.title,
        totalRecipients,
        status: campaign.status,
        runAt,
        sentRate: totalRecipients > 0 ? sentRate : undefined,
        openRate: sentCount > 0 ? openRate : undefined,
        clickRate: sentCount > 0 ? clickRate : undefined,
      };

      recentCampaigns.push(recentCampaign);
    }

    // Đếm tổng số campaign email của user
    const totalCampaigns = await this.userCampaignRepository.count({
      where: {
        userId,
        platform: 'email'
      },
    });

    return {
      campaigns: recentCampaigns,
      totalCampaigns,
      updatedAt: Math.floor(Date.now() / 1000),
    };
  }

  /**
   * Lấy danh sách email campaign với phân trang và filter
   * @param userId ID của người dùng
   * @param queryDto Tham số query
   * @returns Danh sách email campaign với phân trang
   */
  async getCampaigns(userId: number, queryDto: EmailCampaignQueryDto): Promise<PaginatedResult<EmailCampaignItemDto>> {
    this.logger.log(`Lấy danh sách email campaign cho user ${userId} với params:`, queryDto);

    const { page, limit, search, sortBy, sortDirection, status, title, subject } = queryDto;

    // Xây dựng điều kiện where
    const where: any = {
      userId,
      platform: 'email' // Chỉ lấy email campaigns
    };

    if (status) {
      where.status = status;
    }

    if (title) {
      where.title = Like(`%${title}%`);
    }

    if (subject) {
      where.subject = Like(`%${subject}%`);
    }

    if (search) {
      // Tìm kiếm trong cả title và subject
      where.title = Like(`%${search}%`);
    }

    // Xây dựng options cho query
    const options: FindManyOptions<UserCampaign> = {
      where,
      skip: (page - 1) * limit,
      take: limit,
      order: {
        [sortBy || 'createdAt']: sortDirection || 'DESC',
      },
    };

    // Thực hiện query
    const [campaigns, total] = await this.userCampaignRepository.findAndCount(options);

    // Chuyển đổi thành DTO với thống kê
    const data: EmailCampaignItemDto[] = [];

    for (const campaign of campaigns) {
      // Tính toán thống kê cho từng campaign
      const stats = await this.calculateCampaignStats(campaign.id);

      const item: EmailCampaignItemDto = {
        id: campaign.id,
        title: campaign.title,
        description: campaign.description,
        subject: campaign.subject,
        status: campaign.status,
        scheduledAt: campaign.scheduledAt > 0 ? campaign.scheduledAt : undefined,
        createdAt: campaign.createdAt,
        updatedAt: campaign.updatedAt,
        totalRecipients: stats.totalRecipients,
        sentCount: stats.sentCount,
        sentRate: stats.sentRate,
        clickedCount: stats.clickedCount,
        clickRate: stats.clickRate,
      };

      data.push(item);
    }

    return {
      items: data,
      meta: {
        totalItems: total,
        itemCount: data.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(total / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Tính toán thống kê cho một campaign
   * @param campaignId ID của campaign
   * @returns Thống kê campaign
   */
  private async calculateCampaignStats(campaignId: number) {
    // Đếm tổng số người nhận từ campaign history
    const totalRecipients = await this.userCampaignHistoryRepository.count({
      where: { campaignId },
    });

    // Đếm số lượng đã gửi thành công
    const sentCount = await this.userCampaignHistoryRepository.count({
      where: {
        campaignId,
        status: SendStatus.SENT
      },
    });

    // Đếm số lượng đã click
    const clickedCount = await this.userCampaignHistoryRepository.count({
      where: {
        campaignId,
        status: SendStatus.CLICKED
      },
    });

    // Tính tỷ lệ
    const sentRate = totalRecipients > 0 ? Math.round((sentCount / totalRecipients) * 100 * 10) / 10 : 0;
    const clickRate = sentCount > 0 ? Math.round((clickedCount / sentCount) * 100 * 10) / 10 : 0;

    return {
      totalRecipients,
      sentCount,
      clickedCount,
      sentRate: totalRecipients > 0 ? sentRate : undefined,
      clickRate: sentCount > 0 ? clickRate : undefined,
    };
  }

  /**
   * Lấy tổng quan dashboard thống kê email marketing
   * @param userId ID của người dùng
   * @returns Thống kê tổng quan
   */
  async getOverviewDashboard(userId: number): Promise<OverviewDashboardDto> {
    this.logger.log(`Lấy overview dashboard cho user ${userId}`);

    // Lấy tất cả campaigns của user
    const campaigns = await this.userCampaignRepository.find({
      where: {
        userId,
        platform: 'email'
      },
    });

    const campaignIds = campaigns.map(c => c.id);

    if (campaignIds.length === 0) {
      return {
        totalSent: 0,
        totalOpened: 0,
        totalClicks: 0,
        totalUnsubscribed: 0,
      };
    }

    // Lấy tất cả history của các campaigns
    const allHistory = await this.userCampaignHistoryRepository.find({
      where: { campaignId: In(campaignIds) },
    });

    // Tính toán các metrics
    const totalSent = allHistory.filter(h =>
      h.status === SendStatus.SENT ||
      h.status === SendStatus.DELIVERED ||
      h.status === SendStatus.OPENED ||
      h.status === SendStatus.CLICKED
    ).length;

    const totalOpened = allHistory.filter(h =>
      h.status === SendStatus.OPENED ||
      h.status === SendStatus.CLICKED
    ).length;

    const totalClicks = allHistory.filter(h =>
      h.status === SendStatus.CLICKED
    ).length;

    // Giả sử unsubscribed được lưu với status 'unsubscribed' hoặc có thể tính từ failed
    const totalUnsubscribed = allHistory.filter(h =>
      h.status === 'unsubscribed'
    ).length;

    return {
      totalSent,
      totalOpened,
      totalClicks,
      totalUnsubscribed,
    };
  }

  /**
   * Lấy các chỉ số hiệu suất
   * @param userId ID của người dùng
   * @returns Các tỷ lệ hiệu suất
   */
  async getPerformanceMetrics(userId: number): Promise<PerformanceMetricsDto> {
    this.logger.log(`Lấy performance metrics cho user ${userId}`);

    const overview = await this.getOverviewDashboard(userId);

    // Tính toán các tỷ lệ
    const openRate = overview.totalSent > 0
      ? Math.round((overview.totalOpened / overview.totalSent) * 100 * 10) / 10
      : 0;

    const clickRate = overview.totalOpened > 0
      ? Math.round((overview.totalClicks / overview.totalOpened) * 100 * 10) / 10
      : 0;

    // Lấy campaigns để tính bounce rate
    const campaigns = await this.userCampaignRepository.find({
      where: {
        userId,
        platform: 'email'
      },
    });

    const campaignIds = campaigns.map(c => c.id);
    let totalBounced = 0;

    if (campaignIds.length > 0) {
      const allHistory = await this.userCampaignHistoryRepository.find({
        where: { campaignId: In(campaignIds) },
      });

      totalBounced = allHistory.filter(h => h.status === SendStatus.FAILED).length;
    }

    const bounceRate = overview.totalSent > 0
      ? Math.round((totalBounced / overview.totalSent) * 100 * 10) / 10
      : 0;

    const unsubscribeRate = overview.totalSent > 0
      ? Math.round((overview.totalUnsubscribed / overview.totalSent) * 100 * 10) / 10
      : 0;

    return {
      openRate,
      clickRate,
      bounceRate,
      unsubscribeRate,
    };
  }

  /**
   * Lấy xu hướng theo thời gian cho biểu đồ
   * @param userId ID của người dùng
   * @param queryDto Tham số query với startDate và endDate
   * @returns Dữ liệu xu hướng theo ngày
   */
  async getTrendChart(userId: number, queryDto: TrendsQueryDto): Promise<TrendChartDto> {
    this.logger.log(`Lấy trend chart cho user ${userId}`, queryDto);

    // Xác định khoảng thời gian
    const now = Math.floor(Date.now() / 1000);
    const startDate = queryDto.startDate || (now - 30 * 24 * 60 * 60); // 30 ngày trước
    const endDate = queryDto.endDate || now;

    // Lấy campaigns trong khoảng thời gian
    const campaigns = await this.userCampaignRepository.find({
      where: {
        userId,
        platform: 'email',
        createdAt: Between(startDate, endDate),
      },
    });

    const campaignIds = campaigns.map(c => c.id);

    if (campaignIds.length === 0) {
      return {
        dates: [],
        sent: [],
        opened: [],
        clicked: [],
      };
    }

    // Lấy history trong khoảng thời gian
    const allHistory = await this.userCampaignHistoryRepository.find({
      where: {
        campaignId: In(campaignIds),
        createdAt: Between(startDate, endDate),
      },
    });

    // Nhóm dữ liệu theo ngày
    const dailyData: { [date: string]: { sent: number; opened: number; clicked: number } } = {};

    allHistory.forEach(history => {
      const date = new Date(history.createdAt * 1000).toISOString().split('T')[0]; // YYYY-MM-DD

      if (!dailyData[date]) {
        dailyData[date] = { sent: 0, opened: 0, clicked: 0 };
      }

      // Đếm sent
      if ([SendStatus.SENT, SendStatus.DELIVERED, SendStatus.OPENED, SendStatus.CLICKED].includes(history.status as SendStatus)) {
        dailyData[date].sent++;
      }

      // Đếm opened
      if ([SendStatus.OPENED, SendStatus.CLICKED].includes(history.status as SendStatus)) {
        dailyData[date].opened++;
      }

      // Đếm clicked
      if (history.status === SendStatus.CLICKED) {
        dailyData[date].clicked++;
      }
    });

    // Sắp xếp theo ngày và tạo arrays
    const sortedDates = Object.keys(dailyData).sort();
    const dates = sortedDates;
    const sent = sortedDates.map(date => dailyData[date].sent);
    const opened = sortedDates.map(date => dailyData[date].opened);
    const clicked = sortedDates.map(date => dailyData[date].clicked);

    return {
      dates,
      sent,
      opened,
      clicked,
    };
  }

  /**
   * So sánh các chiến dịch
   * @param userId ID của người dùng
   * @returns Dữ liệu so sánh chiến dịch
   */
  async getCampaignComparison(userId: number): Promise<CampaignComparisonDto> {
    this.logger.log(`Lấy campaign comparison cho user ${userId}`);

    // Lấy top 10 campaigns gần đây
    const campaigns = await this.userCampaignRepository.find({
      where: {
        userId,
        platform: 'email'
      },
      order: { createdAt: 'DESC' },
      take: 10,
    });

    const comparisonData: Array<{
      name: string;
      sent: number;
      opened: number;
      clicked: number;
    }> = [];

    for (const campaign of campaigns) {
      const history = await this.userCampaignHistoryRepository.find({
        where: { campaignId: campaign.id },
      });

      const sent = history.filter(h =>
        [SendStatus.SENT, SendStatus.DELIVERED, SendStatus.OPENED, SendStatus.CLICKED].includes(h.status as SendStatus)
      ).length;

      const opened = history.filter(h =>
        [SendStatus.OPENED, SendStatus.CLICKED].includes(h.status as SendStatus)
      ).length;

      const clicked = history.filter(h =>
        h.status === SendStatus.CLICKED
      ).length;

      comparisonData.push({
        name: campaign.title,
        sent,
        opened,
        clicked,
      });
    }

    return {
      campaigns: comparisonData,
    };
  }

  /**
   * Lấy danh sách hiệu quả từng chiến dịch có phân trang
   * @param userId ID của người dùng
   * @param queryDto Tham số query với pagination
   * @returns Danh sách hiệu quả chiến dịch với phân trang
   */
  async getCampaignPerformanceList(userId: number, queryDto: QueryDto): Promise<CampaignPerformanceListDto> {
    this.logger.log(`Lấy campaign performance list cho user ${userId}`, queryDto);

    const { page, limit, sortBy, sortDirection } = queryDto;

    // Lấy campaigns với phân trang
    const [campaigns, total] = await this.userCampaignRepository.findAndCount({
      where: {
        userId,
        platform: 'email'
      },
      skip: (page - 1) * limit,
      take: limit,
      order: {
        [sortBy || 'createdAt']: sortDirection || 'DESC',
      },
    });

    const performanceData: CampaignPerformanceItemDto[] = [];

    for (const campaign of campaigns) {
      const history = await this.userCampaignHistoryRepository.find({
        where: { campaignId: campaign.id },
      });

      const recipients = history.length;
      const totalEmails = recipients; // Tổng số email của chiến dịch

      const opened = history.filter(h =>
        [SendStatus.OPENED, SendStatus.CLICKED].includes(h.status as SendStatus)
      ).length;

      const clicked = history.filter(h =>
        h.status === SendStatus.CLICKED
      ).length;

      // Tính tỷ lệ
      const openRate = recipients > 0 ? Math.round((opened / recipients) * 100 * 10) / 10 : 0;
      const clickRate = opened > 0 ? Math.round((clicked / opened) * 100 * 10) / 10 : 0;

      performanceData.push({
        id: campaign.id,
        name: campaign.title,
        recipients,
        opened,
        openRate,
        clicked,
        clickRate,
        status: campaign.status,
        totalEmails,
      });
    }

    // Tạo thông tin phân trang
    const totalPages = Math.ceil(total / limit);
    const meta = {
      total,
      page,
      limit,
      totalPages,
      hasPreviousPage: page > 1,
      hasNextPage: page < totalPages,
    };

    return {
      data: performanceData,
      meta,
    };
  }

  /**
   * Lấy thống kê tổng quan email campaign (legacy method)
   * @param userId ID của người dùng
   * @returns Thống kê tổng quan
   */
  async getOverview(userId: number): Promise<EmailCampaignOverviewResponseDto> {
    this.logger.log(`Lấy thống kê tổng quan email campaign cho user ${userId}`);

    // Đếm tổng số campaign email của user
    const totalCampaigns = await this.userCampaignRepository.count({
      where: {
        userId,
        platform: 'email'
      },
    });

    // Đếm số campaign đang gửi
    const sendingCampaigns = await this.userCampaignRepository.count({
      where: {
        userId,
        platform: 'email',
        status: CampaignStatus.SENDING
      },
    });

    // Đếm số campaign đã gửi
    const sentCampaigns = await this.userCampaignRepository.count({
      where: {
        userId,
        platform: 'email',
        status: CampaignStatus.SENT
      },
    });

    // Đếm số campaign đã lên lịch
    const scheduledCampaigns = await this.userCampaignRepository.count({
      where: {
        userId,
        platform: 'email',
        status: CampaignStatus.SCHEDULED
      },
    });

    return {
      overview: {
        totalCampaigns,
        sendingCampaigns,
        sentCampaigns,
        scheduledCampaigns,
        updatedAt: Math.floor(Date.now() / 1000),
      }
    };
  }

  /**
   * Tạo email campaign với template và đẩy jobs vào queue
   * @param userId ID của người dùng
   * @param createDto Dữ liệu tạo campaign với template
   * @returns Thông tin campaign và jobs đã tạo
   */
  @Transactional()
  async createEmailCampaignWithTemplate(userId: number, createDto: CreateEmailCampaignWithTemplateDto): Promise<CreateEmailCampaignWithTemplateResponseDto> {
    this.logger.log(`Tạo email campaign với template cho user ${userId}: ${createDto.title}`);

    // Validate template email tồn tại và thuộc về user
    const template = await this.userTemplateEmailRepository.findById(parseInt(createDto.templateEmailId), userId);
    if (!template) {
      throw new NotFoundException(`Template email với ID ${createDto.templateEmailId} không tồn tại hoặc không thuộc về người dùng này`);
    }

    // Validate email server configuration tồn tại và thuộc về user
    const emailServer = await this.emailServerConfigurationUserService.findOne(parseInt(createDto.serverId), userId);
    if (!emailServer) {
      throw new NotFoundException(`Email server configuration với ID ${createDto.serverId} không tồn tại hoặc không thuộc về người dùng này`);
    }

    // Validate segment tồn tại
    const segment = await this.userSegmentService.findOne(userId, parseInt(createDto.segmentId));
    if (!segment) {
      throw new NotFoundException(`Segment với ID ${createDto.segmentId} không tồn tại`);
    }

    // Tạo campaign trong database (giữ nguyên template, không thay thế variables ở đây)
    const campaign = new UserCampaign();
    campaign.userId = userId;
    campaign.title = createDto.title;
    campaign.description = createDto.description || '';
    campaign.platform = 'email';
    campaign.subject = template.subject; // Giữ nguyên template với {{variables}}
    campaign.content = template.htmlContent || template.content || ''; // Giữ nguyên template với {{variables}}
    campaign.server = {
      id: emailServer.id,
      serverName: emailServer.serverName,
      host: emailServer.host,
      port: emailServer.port,
      username: emailServer.username,
      useSsl: emailServer.useSsl,
      useStartTls: emailServer.useStartTls,
    };
    campaign.scheduledAt = createDto.scheduledAt || 0;
    campaign.segmentId = parseInt(createDto.segmentId);
    campaign.audienceIds = null;
    campaign.status = createDto.scheduledAt ? CampaignStatus.SCHEDULED : CampaignStatus.SENDING;
    campaign.createdAt = Math.floor(Date.now() / 1000);
    campaign.updatedAt = Math.floor(Date.now() / 1000);

    const savedCampaign = await this.userCampaignRepository.save(campaign);
    this.logger.log(`Campaign đã được lưu với ID: ${savedCampaign.id}`);

    // Lấy danh sách audience từ segment
    const audiences = await this.getAudiencesForCampaign(userId, savedCampaign);
    this.logger.log(`Tìm thấy ${audiences.length} audience cho campaign`);

    if (audiences.length === 0) {
      throw new BadRequestException('Không tìm thấy audience nào trong segment để gửi email');
    }

    // Tạo batch job cho queue với template ID và template variables
    const jobIds = await this.createEmailJobs(savedCampaign, audiences, parseInt(createDto.templateEmailId), createDto.templateVariables);

    // Lưu job IDs vào campaign để có thể hủy sau này
    savedCampaign.jobIds = jobIds;
    await this.userCampaignRepository.save(savedCampaign);

    this.logger.log(`Đã tạo batch job cho campaign ${savedCampaign.id} với ${audiences.length} recipients`);

    return {
      campaignId: savedCampaign.id,
      scheduledAt: savedCampaign.scheduledAt || undefined,
      status: savedCampaign.status,
      template: {
        id: template.id,
        name: template.name,
        subject: template.subject,
      },
      emailServer: {
        id: emailServer.id,
        serverName: emailServer.serverName,
        host: emailServer.host,
      },
    };
  }

  /**
   * Hủy job trong queue cho campaign
   * @param campaign Campaign cần hủy job
   * @returns Promise<void>
   */
  private async cancelCampaignJobs(campaign: UserCampaign): Promise<void> {
    if (!campaign.jobIds || campaign.jobIds.length === 0) {
      this.logger.log(`Campaign ${campaign.id} không có job nào để hủy`);
      return;
    }

    for (const jobId of campaign.jobIds) {
      try {
        const job = await this.emailMarketingQueue.getJob(jobId);
        if (job) {
          const jobState = await job.getState();
          this.logger.log(`Job ${jobId} state: ${jobState}`);

          if (jobState === 'waiting' || jobState === 'delayed') {
            // Job chưa chạy, có thể remove
            await job.remove();
            this.logger.log(`✅ Đã hủy job ${jobId} cho campaign ${campaign.id}`);
          } else if (jobState === 'active') {
            // Job đang chạy, không thể hủy nhưng có thể đánh dấu campaign là cancelled
            this.logger.warn(`⚠️ Job ${jobId} đang chạy, không thể hủy`);
          } else {
            this.logger.log(`Job ${jobId} đã hoàn thành hoặc thất bại, không cần hủy`);
          }
        } else {
          this.logger.log(`Job ${jobId} không tồn tại trong queue`);
        }
      } catch (error) {
        this.logger.error(`Lỗi khi hủy job ${jobId} cho campaign ${campaign.id}:`, error);
      }
    }
  }

  /**
   * Xóa nhiều email campaign
   * @param userId ID của người dùng
   * @param ids Danh sách ID campaign cần xóa
   * @returns Kết quả xóa nhiều
   */
  @Transactional()
  async bulkDeleteEmailCampaigns(userId: number, ids: number[]): Promise<BulkDeleteResponseDto> {
    const deletedIds: number[] = [];
    const failedIds: number[] = [];

    for (const id of ids) {
      try {
        const campaign = await this.userCampaignRepository.findOne({ where: { id, userId } });
        if (!campaign) {
          failedIds.push(id);
          continue;
        }

        // Hủy job trong queue cho campaign đang chạy hoặc đã lên lịch
        if (campaign.status === CampaignStatus.SCHEDULED || campaign.status === CampaignStatus.SENDING) {
          this.logger.log(`Hủy job cho campaign ${id} với status ${campaign.status}`);
          await this.cancelCampaignJobs(campaign);
        }

        // Xóa lịch sử campaign
        await this.userCampaignHistoryRepository.delete({ campaignId: id });

        // Xóa campaign
        await this.userCampaignRepository.remove(campaign);
        deletedIds.push(id);
      } catch (error) {
        this.logger.error(`Lỗi khi xóa campaign ${id}:`, error);
        failedIds.push(id);
      }
    }

    const deletedCount = deletedIds.length;
    const failedCount = failedIds.length;
    const message = failedCount > 0
      ? `Đã xóa ${deletedCount} email campaign thành công, ${failedCount} campaign không thể xóa`
      : `Đã xóa ${deletedCount} email campaign thành công`;

    return {
      deletedCount,
      failedCount,
      deletedIds,
      failedIds,
      message,
    };
  }
}
