import { EmployeeInfoSimpleDto } from '@/modules/employee/dto/employee-info-simple.dto';
import { EmployeeInfoService } from '@/modules/employee/services/employee-info.service';
import { CdnService } from '@/shared/services/cdn.service';
import { S3Service } from '@/shared/services/s3.service';
import { CategoryFolderEnum, FileSizeEnum, generateS3Key, ImageType, TimeIntervalEnum } from '@/shared/utils';
import { FileJsonlType } from '@/shared/utils/file/file-jsonl-type.util';
import { AppException } from '@common/exceptions';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { Transactional } from 'typeorm-transactional';
import { DataFineTuneStatus } from '../../constants/data-fine-tune-status.enum';
import { MODELS_ERROR_CODES } from '../../exceptions';
import { AdminDataFineTuneRepository } from '../../repositories/admin-data-fine-tune.repository';
import {
  AdminDataFineTuneDetailResponseDto,
  AdminDataFineTuneQueryDto,
  AdminDataFineTuneResponseDto,
  CreateAdminDataFineTuneDto,
  UpdateAdminDataFineTuneDto,
} from '../dto/data-fine-tune';
import { AdminDataFineTuneMapper } from '../mappers/admin-data-fine-tune.mapper';
import { ProviderFineTuneEnum } from '../../constants';

/**
 * Service xử lý business logic cho Admin Data Fine Tune
 */
@Injectable()
export class AdminDataFineTuneService {
  private readonly logger = new Logger(AdminDataFineTuneService.name);

  constructor(
    private readonly adminDataFineTuneRepository: AdminDataFineTuneRepository,
    private readonly s3Service: S3Service,
    private readonly cdnService: CdnService,
    private readonly employeeInfoService: EmployeeInfoService,
    private readonly httpService: HttpService,
  ) { }

  /**
   * Tạo mới dataset fine tune
   */
  @Transactional()
  async create(createDto: CreateAdminDataFineTuneDto, employeeId: number): Promise<ApiResponseDto<{ id: string, trainUploadUrl: string, validUploadUrl: string | null }>> {
    this.logger.log(`Creating data fine tune by employee ${employeeId}`);

    // Kiểm tra trùng tên
    const existsByName = await this.adminDataFineTuneRepository.existsByName(createDto.name);
    if (existsByName) {
      throw new AppException(MODELS_ERROR_CODES.ADMIN_DATA_FINE_TUNE_NAME_EXISTS);
    }

    const trainDatasetKey = generateS3Key({
      baseFolder: employeeId.toString(),
      categoryFolder: CategoryFolderEnum.DATA_FINE_TUNE,
      fileName: `train-${employeeId}.jsonl`,
      useTimeFolder: true,
    });

    const trainUploadUrl = await this.s3Service.createPresignedWithID(
      trainDatasetKey,
      TimeIntervalEnum.ONE_HOUR,
      FileJsonlType.getMimeType(createDto.trainDataset),
      FileSizeEnum.TEN_MB,
    );

    let validDatasetKey: string | null = null;
    let validUploadUrl: string | null = null;
    if (createDto.validDataset && createDto.provider === ProviderFineTuneEnum.OPENAI) {
      validDatasetKey = generateS3Key({
        baseFolder: employeeId.toString(),
        categoryFolder: CategoryFolderEnum.DATA_FINE_TUNE,
        fileName: `valid-${employeeId}.jsonl`,
        useTimeFolder: true,
      });

      validUploadUrl = await this.s3Service.createPresignedWithID(
        validDatasetKey,
        TimeIntervalEnum.ONE_HOUR,
        FileJsonlType.getMimeType(createDto.validDataset),
        FileSizeEnum.TEN_MB,
      );
    }

    // Tạo entity mới
    const newDataset = this.adminDataFineTuneRepository.create({
      name: createDto.name,
      description: createDto.description,
      trainDataset: trainDatasetKey,
      validDataset: validDatasetKey,
      createdBy: employeeId,
      updatedAt: Date.now(),
      status: DataFineTuneStatus.PENDING,
      provider: createDto.provider,
      createdAt: Date.now(),
      updatedBy: employeeId
    });

    // Lưu vào database
    const savedDataset = await this.adminDataFineTuneRepository.save(newDataset);

    this.logger.log(`Created data fine tune ${newDataset.id} successfully`);
    return ApiResponseDto.success({ id: savedDataset.id, trainUploadUrl, validUploadUrl });
  }

  /**
   * Lấy danh sách dataset fine tune có phân trang
   */
  async findAll(queryDto: AdminDataFineTuneQueryDto): Promise<ApiResponseDto<PaginatedResult<AdminDataFineTuneResponseDto>>> {
    this.logger.log('Getting data fine tune list');

    // Lấy dữ liệu từ repository
    const result = await this.adminDataFineTuneRepository.findWithPagination(queryDto);

    // Convert sang DTO
    const items = AdminDataFineTuneMapper.toResponseDtoArray(result.items);

    return ApiResponseDto.paginated({
      items,
      meta: result.meta
    });
  }

  /**
   * Lấy chi tiết dataset fine tune
   */
  async findOne(id: string): Promise<ApiResponseDto<AdminDataFineTuneDetailResponseDto>> {
    this.logger.log(`Getting data fine tune detail: ${id}`);

    // Tìm dataset với đầy đủ dữ liệu
    const dataset = await this.adminDataFineTuneRepository.findByIdWithFullData(id);
    if (!dataset) {
      throw new AppException(MODELS_ERROR_CODES.ADMIN_DATA_FINE_TUNE_NOT_FOUND);
    }

    // Lấy thông tin employee
    const employeeIds: number[] = [];
    if (dataset.createdBy) employeeIds.push(dataset.createdBy);
    if (dataset.updatedBy) employeeIds.push(dataset.updatedBy);

    const employeeInfoMap = await this.employeeInfoService.getEmployeeInfoMap(employeeIds);

    // Tạo download URLs cho datasets
    let trainDatasetUrl: string | null = null;
    let validDatasetUrl: string | null = null;

    try {
      trainDatasetUrl = this.cdnService.generateUrlView(dataset.trainDataset, TimeIntervalEnum.ONE_HOUR) || null;

      if (dataset.validDataset) {
        validDatasetUrl = this.cdnService.generateUrlView(dataset.validDataset, TimeIntervalEnum.ONE_HOUR);
      }
    } catch (error) {
      this.logger.warn(`Failed to generate CDN URLs for dataset ${id}: ${error.message}`);
    }

    // Chuẩn bị thông tin employee
    const createdEmployee: EmployeeInfoSimpleDto | null = dataset.createdBy
      ? employeeInfoMap.get(dataset.createdBy) || null
      : null;

    const updatedEmployee: EmployeeInfoSimpleDto | null = dataset.createdBy
      ? employeeInfoMap.get(dataset.createdBy) || null
      : null;

    // Sử dụng mapper để chuyển đổi sang DTO
    const responseDto = AdminDataFineTuneMapper.toDetailResponseDto(
      dataset,
      trainDatasetUrl,
      validDatasetUrl,
      createdEmployee,
      updatedEmployee
    );

    return ApiResponseDto.success(responseDto);
  }

  /**
   * Cập nhật dataset fine tune
   */
  @Transactional()
  async update(id: string, updateDto: UpdateAdminDataFineTuneDto, employeeId: number): Promise<ApiResponseDto<{ id: string, trainUploadUrl: string | null, validUploadUrl: string | null }>> {
    this.logger.log(`Updating data fine tune ${id} by employee ${employeeId}`);

    // Tìm dataset hiện tại
    const existingDataset = await this.adminDataFineTuneRepository.findByIdWithFullData(id);
    if (!existingDataset) {
      throw new AppException(MODELS_ERROR_CODES.ADMIN_DATA_FINE_TUNE_NOT_FOUND);
    }

    // Kiểm tra trùng tên (nếu có thay đổi tên)
    if (updateDto.name && updateDto.name !== existingDataset.name) {
      const existsByName = await this.adminDataFineTuneRepository.existsByName(updateDto.name, id);
      if (existsByName) {
        throw new AppException(MODELS_ERROR_CODES.ADMIN_DATA_FINE_TUNE_NAME_EXISTS);
      }
    }

    // Cập nhật các trường
    if (updateDto.name !== undefined) {
      existingDataset.name = updateDto.name;
    }
    if (updateDto.description !== undefined) {
      existingDataset.description = updateDto.description;
    }

    let trainUploadUrl: string | null = null;
    let validUploadUrl: string | null = null;

    if (updateDto.trainDataset !== undefined) {
      trainUploadUrl = await this.s3Service.createPresignedWithID(
        existingDataset.trainDataset,
        TimeIntervalEnum.ONE_HOUR,
        FileJsonlType.getMimeType(updateDto.trainDataset),
        FileSizeEnum.TEN_MB,
      );
    }

    if (updateDto.validDataset !== undefined && existingDataset.provider === ProviderFineTuneEnum.OPENAI) {
      if (existingDataset.validDataset) {
        validUploadUrl = await this.s3Service.createPresignedWithID(
          existingDataset.validDataset,
          TimeIntervalEnum.ONE_HOUR,
          FileJsonlType.getMimeType(updateDto.validDataset),
          FileSizeEnum.TEN_MB,
        );
      } else {
        const validDatasetKey = generateS3Key({
          baseFolder: employeeId.toString(),
          categoryFolder: CategoryFolderEnum.DATA_FINE_TUNE,
          fileName: `valid-${employeeId}.jsonl`,
          useTimeFolder: true,
        });

        validUploadUrl = await this.s3Service.createPresignedWithID(
          validDatasetKey,
          TimeIntervalEnum.ONE_HOUR,
          FileJsonlType.getMimeType(updateDto.validDataset),
          FileSizeEnum.TEN_MB,
        );
      }
    }

    existingDataset.updatedBy = employeeId;
    existingDataset.updatedAt = Date.now();

    // Lưu thay đổi
    await this.adminDataFineTuneRepository.update({ id }, existingDataset);

    this.logger.log(`Updated data fine tune ${id} successfully`);
    return ApiResponseDto.success({
      id,
      trainUploadUrl,
      validUploadUrl
    });
  }

  /**
   * Xóa dataset fine tune (soft delete) - hỗ trợ bulk delete
   */
  @Transactional()
  async remove(ids: string[], employeeId: number): Promise<ApiResponseDto<{
    message: string;
    deletedIds: string[];
    errorIds: string[];
    totalRequested: number;
    totalDeleted: number;
  }>> {
    this.logger.log(`Bulk soft deleting data fine tune [${ids.join(', ')}] by employee ${employeeId}`);

    // Validate input
    if (!ids || ids.length === 0) {
      throw new AppException(MODELS_ERROR_CODES.ADMIN_DATA_FINE_TUNE_NOT_FOUND);
    }

    // Lọc ra các ID hợp lệ (tồn tại và chưa bị xóa)
    const validIds = await this.adminDataFineTuneRepository.findValidIds(ids);
    const errorIds = ids.filter(id => !validIds.includes(id));

    let deletedIds: string[] = [];
    let actualDeleted = 0;

    // Thực hiện bulk soft delete cho các ID hợp lệ
    if (validIds.length > 0) {
      actualDeleted = await this.adminDataFineTuneRepository.bulkSoftDeleteDatasets(validIds, employeeId);

      // Nếu số lượng deleted khác với số lượng valid IDs, có thể có race condition
      if (actualDeleted === validIds.length) {
        deletedIds = validIds;
      } else {
        // Kiểm tra lại để xác định chính xác ID nào đã bị xóa
        const remainingValidIds = await this.adminDataFineTuneRepository.findValidIds(validIds);
        deletedIds = validIds.filter(id => !remainingValidIds.includes(id));

        // Cập nhật errorIds với những ID không thể xóa
        const failedIds = validIds.filter(id => remainingValidIds.includes(id));
        errorIds.push(...failedIds);
      }
    }

    // Tạo message phù hợp
    let message = '';
    if (deletedIds.length === ids.length) {
      message = `Xóa thành công ${deletedIds.length} dataset fine tune`;
    } else if (deletedIds.length > 0) {
      message = `Xóa thành công ${deletedIds.length}/${ids.length} dataset fine tune`;
    } else {
      message = 'Không có dataset nào được xóa';
    }

    this.logger.log(`Bulk delete completed: ${deletedIds.length} deleted, ${errorIds.length} errors`);

    return ApiResponseDto.success({
      message,
      deletedIds,
      errorIds,
      totalRequested: ids.length,
      totalDeleted: deletedIds.length
    });
  }

  /**
   * Lấy danh sách dataset fine tune đã xóa
   */
  async findDeleted(queryDto: AdminDataFineTuneQueryDto): Promise<ApiResponseDto<PaginatedResult<AdminDataFineTuneResponseDto>>> {
    this.logger.log('Getting deleted data fine tune list');

    // Lấy dữ liệu từ repository
    const result = await this.adminDataFineTuneRepository.findDeletedWithPagination(queryDto);

    // Convert sang DTO
    const items = AdminDataFineTuneMapper.toResponseDtoArray(result.items);

    return ApiResponseDto.paginated({
      items,
      meta: result.meta
    });
  }

  /**
   * Khôi phục dataset fine tune đã xóa
   */
  @Transactional()
  async restore(id: string, employeeId: number): Promise<ApiResponseDto<{ message: string }>> {
    this.logger.log(`Restoring data fine tune ${id} by employee ${employeeId}`);

    // Thực hiện khôi phục
    const restored = await this.adminDataFineTuneRepository.restoreCustom(id);

    if (!restored) {
      throw new AppException(MODELS_ERROR_CODES.ADMIN_DATA_FINE_TUNE_RESTORE_FAILED);
    }

    this.logger.log(`Restored data fine tune ${id} successfully`);
    return ApiResponseDto.success({ message: 'Khôi phục dataset fine tune thành công' });
  }

  /**
   * Cập nhật trạng thái dataset từ PENDING thành APPROVED hoặc ERROR
   */
  @Transactional()
  async updateStatus(id: string, employeeId: number): Promise<ApiResponseDto<{
    message: string;
    id: string;
  }>> {
    this.logger.log(`Updating status for dataset ${id} by employee ${employeeId}`);

    // Tìm dataset hiện tại
    const dataset = await this.adminDataFineTuneRepository.findByIdWithFullData(id);
    if (!dataset) {
      throw new AppException(MODELS_ERROR_CODES.ADMIN_DATA_FINE_TUNE_NOT_FOUND);
    }

    // Kiểm tra trạng thái hiện tại có phải PENDING không
    if (dataset.status !== DataFineTuneStatus.PENDING) {
      throw new AppException(MODELS_ERROR_CODES.ADMIN_DATA_FINE_TUNE_INVALID_STATUS_TRANSITION);
    }

    // Nếu status = APPROVED, kiểm tra file tồn tại trên S3
    const trainExists = await this.s3Service.checkObjectExists(dataset.trainDataset);
    if (!trainExists) {
      dataset.status = DataFineTuneStatus.ERROR;
    } else {
      dataset.status = DataFineTuneStatus.APPROVED;
    }

    if (dataset.validDataset) {
      const validExists = await this.s3Service.checkObjectExists(dataset.validDataset);
      if (!validExists) {
        dataset.status = DataFineTuneStatus.ERROR;
      } else {
        dataset.status = DataFineTuneStatus.APPROVED;
      }
    }

    // Cập nhật trạng thái trong database
    const updated = await this.adminDataFineTuneRepository.updateStatus(id, dataset.status, employeeId);

    if (!updated) {
      throw new AppException(MODELS_ERROR_CODES.ADMIN_DATA_FINE_TUNE_NOT_FOUND);
    }

    return ApiResponseDto.success({
      message: 'Cập nhật trạng thái dataset thành công',
      id
    });
  }

  /**
   * Lấy URL upload dataset
   */
  async urlUpload(employeeId: number, mime: string): Promise<ApiResponseDto<{ uploadUrl: string, viewUrl: string | null }>> {
    this.logger.log(`Getting upload URL for employee ${employeeId}`);

    const key = generateS3Key({
      baseFolder: employeeId.toString(),
      categoryFolder: CategoryFolderEnum.DATA_FINE_TUNE,
      useTimeFolder: true,
    });

    const uploadUrl = await this.s3Service.createPresignedWithID(
      key,
      TimeIntervalEnum.ONE_HOUR,
      ImageType.getType(mime),
      FileSizeEnum.TEN_MB,
    );

    const viewUrl = this.cdnService.generateUrlView(key, TimeIntervalEnum.ONE_HOUR);

    return ApiResponseDto.success({ uploadUrl, viewUrl });
  }
}
