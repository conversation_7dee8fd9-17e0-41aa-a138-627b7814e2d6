{"openapi": "3.0.0", "info": {"title": "Affiliate Module API", "description": "API documentation for Affiliate Module - Quản lý hệ thống affiliate bao gồm tà<PERSON>, đ<PERSON><PERSON> hàng, ho<PERSON> hồ<PERSON>, rút tiền và thống kê", "version": "1.0.0", "contact": {"name": "RedAI Development Team", "email": "<EMAIL>"}}, "servers": [{"url": "http://localhost:3000", "description": "Development server"}, {"url": "https://api.redai.com", "description": "Production server"}], "tags": [{"name": "Admin - Affiliate Overview", "description": "<PERSON><PERSON><PERSON><PERSON> lý tổng quan affiliate cho admin"}, {"name": "Admin - Affiliate Account", "description": "Quản lý tài khoản affiliate cho admin"}, {"name": "Admin - Affiliate <PERSON>", "description": "Quản lý rút tiền affiliate cho admin"}, {"name": "Admin - Affiliate Rank", "description": "<PERSON><PERSON><PERSON><PERSON> lý cấp b<PERSON>c affiliate cho admin"}, {"name": "Admin - Affiliate Point Conversion", "description": "<PERSON><PERSON><PERSON><PERSON> lý chuyển đổi điểm affiliate cho admin"}, {"name": "Admin - Affiliate <PERSON>", "description": "Quản lý lượt click affiliate cho admin"}, {"name": "Admin - Affiliate Contract", "description": "<PERSON><PERSON><PERSON><PERSON> lý hợp đồng affiliate cho admin"}, {"name": "User - Affiliate Statistics", "description": "Thống kê affiliate cho người dùng"}, {"name": "User - Affiliate Account", "description": "Quản lý tài khoản affiliate cho người dùng"}, {"name": "User - Affiliate Order", "description": "Q<PERSON>ản lý đơn hàng affiliate cho người dùng"}, {"name": "User - Affiliate <PERSON>", "description": "Q<PERSON>ản lý rút tiền affiliate cho người dùng"}, {"name": "User - Affiliate Customer", "description": "<PERSON><PERSON>ản lý khách hàng affiliate cho người dùng"}, {"name": "User - Affiliate Point Conversion", "description": "<PERSON><PERSON><PERSON><PERSON> lý chuyển đổi điểm affiliate cho người dùng"}, {"name": "User - Affiliate Referral Link", "description": "<PERSON><PERSON><PERSON><PERSON> lý liên kết giới thiệu affiliate cho người dùng"}, {"name": "User - Affiliate Upload", "description": "Upload tài liệu affiliate cho người dùng"}, {"name": "User - Affiliate Business", "description": "<PERSON><PERSON><PERSON><PERSON> lý thông tin doanh nghiệp affiliate cho người dùng"}, {"name": "User - Affiliate Registration", "description": "Đăng ký affiliate cho người dùng"}], "paths": {"/admin/affiliate/overview": {"get": {"tags": ["Admin - Affiliate Overview"], "summary": "<PERSON><PERSON><PERSON> thông tin tổng quan về affiliate", "description": "API này trả về thông tin tổng quan về affiliate, bao gồ<PERSON>: tổ<PERSON> số Publisher (tài khoản affiliate), tổng số cấp bậc (Rank), tổng số đơn hàng, và tổng số lần chuyển đổi điểm", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> thông tin tổng quan thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> thông tin tổng quan thành công"}, "data": {"$ref": "#/components/schemas/AffiliateOverviewDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/admin/affiliate/accounts": {"get": {"tags": ["Admin - Affiliate Account"], "summary": "<PERSON><PERSON><PERSON> danh s<PERSON>ch tà<PERSON> k<PERSON> affiliate", "description": "<PERSON><PERSON><PERSON> danh sách tài khoản affiliate với phân trang, hỗ trợ tìm kiếm và lọc theo trạng thái", "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "limit", "in": "query", "description": "<PERSON><PERSON> lượng kết quả mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "example": 10}}, {"name": "search", "in": "query", "description": "Từ khóa tìm kiếm theo tên hoặc email", "required": false, "schema": {"type": "string", "example": "<EMAIL>"}}, {"name": "status", "in": "query", "description": "<PERSON><PERSON><PERSON> theo trạng thái tài k<PERSON>n", "required": false, "schema": {"type": "string", "enum": ["DRAFT", "PENDING_APPROVAL", "APPROVED", "REJECTED", "ACTIVE", "INACTIVE"], "example": "ACTIVE"}}, {"name": "sortBy", "in": "query", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>p xếp", "required": false, "schema": {"type": "string", "enum": ["createdAt", "updatedAt", "totalEarned", "availableBalance"], "default": "createdAt"}}, {"name": "sortDirection", "in": "query", "description": "<PERSON><PERSON><PERSON><PERSON> sắp xếp", "required": false, "schema": {"type": "string", "enum": ["ASC", "DESC"], "default": "DESC"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> danh sách tài khoản affiliate thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> danh sách tài khoản affiliate thành công"}, "data": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/AffiliateAccountDto"}}, "meta": {"$ref": "#/components/schemas/PaginationMeta"}}}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/admin/affiliate/accounts/{id}": {"get": {"tags": ["Admin - Affiliate Account"], "summary": "<PERSON><PERSON><PERSON> thông tin chi tiết tài k<PERSON>n affiliate", "description": "<PERSON><PERSON><PERSON> thông tin chi tiết của một tài khoản affiliate dựa trên ID", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của tài khoản affiliate", "required": true, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> thông tin tài khoản affiliate thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> thông tin tài khoản affiliate thành công"}, "data": {"$ref": "#/components/schemas/AffiliateAccountDto"}}}}}}, "404": {"$ref": "#/components/responses/NotFound"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "patch": {"tags": ["Admin - Affiliate Account"], "summary": "<PERSON><PERSON><PERSON> nhật trạng thái tài <PERSON> affiliate", "description": "<PERSON><PERSON><PERSON> nh<PERSON>t trạng thái của tài k<PERSON>n affiliate", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của tài khoản affiliate", "required": true, "schema": {"type": "integer", "example": 1}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateAffiliateAccountStatusDto"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> nhật trạng thái tài khoản affiliate thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> nhật trạng thái tài khoản affiliate thành công"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "404": {"$ref": "#/components/responses/NotFound"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/admin/affiliate/clicks": {"get": {"tags": ["Admin - Affiliate <PERSON>"], "summary": "<PERSON><PERSON><PERSON> danh s<PERSON>ch <PERSON> click", "description": "<PERSON><PERSON><PERSON> danh s<PERSON>ch lư<PERSON> click với phân trang, hỗ trợ tìm kiếm và lọc theo thời gian", "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "limit", "in": "query", "description": "<PERSON><PERSON> lượng kết quả mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "example": 10}}, {"name": "startDate", "in": "query", "description": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON><PERSON> l<PERSON> (YYYY-MM-DD)", "required": false, "schema": {"type": "string", "format": "date", "example": "2024-01-01"}}, {"name": "endDate", "in": "query", "description": "<PERSON><PERSON><PERSON> kết thúc lọ<PERSON> (YYYY-MM-DD)", "required": false, "schema": {"type": "string", "format": "date", "example": "2024-12-31"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> danh s<PERSON>ch l<PERSON> click thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> danh s<PERSON>ch l<PERSON> click thành công"}, "data": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/AffiliateClickDto"}}, "meta": {"$ref": "#/components/schemas/PaginationMeta"}}}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/affiliate/statistics": {"get": {"tags": ["User - Affiliate Statistics"], "summary": "<PERSON><PERSON><PERSON> thông tin thống kê tài k<PERSON>n affiliate", "description": "<PERSON><PERSON><PERSON> thông tin thống kê tổng quan về tài khoản affiliate của người dùng hiện tại", "security": [{"bearerAuth": []}], "parameters": [{"name": "startDate", "in": "query", "description": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON><PERSON> l<PERSON> (YYYY-MM-DD)", "required": false, "schema": {"type": "string", "format": "date", "example": "2024-01-01"}}, {"name": "endDate", "in": "query", "description": "<PERSON><PERSON><PERSON> kết thúc lọ<PERSON> (YYYY-MM-DD)", "required": false, "schema": {"type": "string", "format": "date", "example": "2024-12-31"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> thông tin thống kê thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> thông tin thống kê thành công"}, "data": {"$ref": "#/components/schemas/AffiliateStatisticsDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/affiliate/account": {"get": {"tags": ["User - Affiliate Account"], "summary": "<PERSON><PERSON><PERSON> thông tin tài khoản affiliate của người dùng", "description": "<PERSON><PERSON><PERSON> thông tin chi tiết tài khoản affiliate của người dùng hiện tại", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> thông tin tài khoản affiliate thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> thông tin tài khoản affiliate thành công"}, "data": {"$ref": "#/components/schemas/UserAffiliateAccountDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/affiliate/withdrawals": {"get": {"tags": ["User - Affiliate <PERSON>"], "summary": "<PERSON><PERSON><PERSON> danh sách yêu cầu rút tiền", "description": "<PERSON><PERSON><PERSON> danh sách yêu cầu rút tiền của người dùng hiện tại với phân trang", "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "limit", "in": "query", "description": "<PERSON><PERSON> lượng kết quả mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "example": 10}}, {"name": "status", "in": "query", "description": "<PERSON><PERSON><PERSON> theo trạng thái yêu cầu rút tiền", "required": false, "schema": {"type": "string", "enum": ["PENDING", "APPROVED", "REJECTED", "COMPLETED"], "example": "PENDING"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> danh sách yêu cầu rút tiền thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> danh sách yêu cầu rút tiền thành công"}, "data": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/AffiliateWithdrawalDto"}}, "meta": {"$ref": "#/components/schemas/PaginationMeta"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "post": {"tags": ["User - Affiliate <PERSON>"], "summary": "<PERSON><PERSON><PERSON> y<PERSON>u c<PERSON>u rút tiền", "description": "<PERSON><PERSON><PERSON> y<PERSON>u cầu rút tiền mới cho tài k<PERSON>n affiliate", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateWithdrawRequestDto"}}}}, "responses": {"201": {"description": "<PERSON><PERSON><PERSON> y<PERSON>u cầu rút tiền thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> y<PERSON>u cầu rút tiền thành công"}, "data": {"$ref": "#/components/schemas/WithdrawResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}, "schemas": {"AffiliateOverviewDto": {"type": "object", "properties": {"totalPublishers": {"type": "integer", "description": "Tổng số Publisher (tài khoản affiliate)", "example": 150}, "totalRanks": {"type": "integer", "description": "<PERSON><PERSON><PERSON> s<PERSON> cấp bậc (Rank)", "example": 5}, "totalOrders": {"type": "integer", "description": "Tổng số đơn hàng", "example": 1250}, "totalPointConversions": {"type": "integer", "description": "Tổng số lần chuyển đổi điểm", "example": 85}, "totalRevenue": {"type": "number", "format": "float", "description": "<PERSON><PERSON>ng doanh thu", "example": 125000.5}, "totalCommission": {"type": "number", "format": "float", "description": "<PERSON><PERSON><PERSON> hoa hồng", "example": 12500.05}, "totalClicks": {"type": "integer", "description": "<PERSON><PERSON>ng số l<PERSON> click", "example": 5000}, "averageConversionRate": {"type": "number", "format": "float", "description": "Tỷ lệ chuyển đổi trung bình (%)", "example": 25.0}}, "required": ["totalPublishers", "totalRanks", "totalOrders", "totalPointConversions", "totalRevenue", "totalCommission", "totalClicks", "averageConversionRate"]}, "AffiliateAccountDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID của tài khoản affiliate", "example": 1}, "userId": {"type": "integer", "description": "ID của người dùng", "example": 123}, "status": {"type": "string", "enum": ["DRAFT", "PENDING_APPROVAL", "APPROVED", "REJECTED", "ACTIVE", "INACTIVE"], "description": "Trạng thái tài k<PERSON>n affiliate", "example": "ACTIVE"}, "totalEarned": {"type": "number", "format": "float", "description": "Tổng số tiền đã kiếm đư<PERSON>", "example": 5000.0}, "totalPaidOut": {"type": "number", "format": "float", "description": "Tổng số tiền đã đư<PERSON><PERSON>h toán", "example": 3000.0}, "availableBalance": {"type": "number", "format": "float", "description": "Số dư hiện tại còn lại", "example": 2000.0}, "performance": {"type": "integer", "description": "Số lượng khách hàng giới thiệu trong 3 tháng gần nhất", "example": 25}, "accountType": {"type": "string", "enum": ["PERSONAL", "BUSINESS"], "description": "<PERSON><PERSON><PERSON> tà<PERSON>", "example": "PERSONAL"}, "createdAt": {"type": "string", "format": "date-time", "description": "<PERSON><PERSON><PERSON><PERSON> gian tạo", "example": "2024-01-15T10:30:00Z"}, "updatedAt": {"type": "string", "format": "date-time", "description": "<PERSON><PERSON><PERSON><PERSON> gian c<PERSON><PERSON> nh<PERSON>t", "example": "2024-01-20T15:45:00Z"}, "user": {"type": "object", "properties": {"id": {"type": "integer", "example": 123}, "email": {"type": "string", "example": "<EMAIL>"}, "fullName": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON>"}}}}, "required": ["id", "userId", "status", "totalEarned", "totalPaidOut", "availableBalance", "accountType", "createdAt", "updatedAt"]}, "AffiliateClickDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID c<PERSON><PERSON> click", "example": 1}, "affiliateAccountId": {"type": "integer", "description": "ID tài khoản affiliate", "example": 1}, "ipAddress": {"type": "string", "description": "Địa chỉ IP của người click", "example": "***********"}, "userAgent": {"type": "string", "description": "User agent c<PERSON><PERSON><PERSON><PERSON>", "example": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"}, "referrer": {"type": "string", "description": "URL trang giới thiệu", "example": "https://example.com/page"}, "clickedAt": {"type": "string", "format": "date-time", "description": "Th<PERSON>i gian <PERSON>", "example": "2024-01-15T10:30:00Z"}}, "required": ["id", "affiliateAccountId", "ip<PERSON><PERSON><PERSON>", "clickedAt"]}, "UpdateAffiliateAccountStatusDto": {"type": "object", "properties": {"status": {"type": "string", "enum": ["DRAFT", "PENDING_APPROVAL", "APPROVED", "REJECTED", "ACTIVE", "INACTIVE"], "description": "Trạng thái mới của tài k<PERSON>ản affiliate", "example": "APPROVED"}}, "required": ["status"]}, "AffiliateStatisticsDto": {"type": "object", "properties": {"totalEarned": {"type": "number", "format": "float", "description": "Tổng số tiền đã kiếm đư<PERSON>", "example": 5000.0}, "availableBalance": {"type": "number", "format": "float", "description": "Số dư hiện tại có thể rút", "example": 2000.0}, "totalClicks": {"type": "integer", "description": "<PERSON><PERSON>ng số l<PERSON> click", "example": 150}, "totalOrders": {"type": "integer", "description": "Tổng số đơn hàng", "example": 25}, "totalCustomers": {"type": "integer", "description": "<PERSON><PERSON>ng số khách hàng giới thiệu", "example": 20}, "conversionRate": {"type": "number", "format": "float", "description": "Tỷ lệ chuyển đổi (%)", "example": 16.67}, "thisMonthEarned": {"type": "number", "format": "float", "description": "<PERSON><PERSON> tiền kiếm đư<PERSON><PERSON> trong tháng này", "example": 500.0}}, "required": ["totalEarned", "availableBalance", "totalClicks", "totalOrders", "totalCustomers", "conversionRate", "thisMonthEarned"]}, "UserAffiliateAccountDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID của tài khoản affiliate", "example": 1}, "status": {"type": "string", "enum": ["DRAFT", "PENDING_APPROVAL", "APPROVED", "REJECTED", "ACTIVE", "INACTIVE"], "description": "Trạng thái tài k<PERSON>n affiliate", "example": "ACTIVE"}, "totalEarned": {"type": "number", "format": "float", "description": "Tổng số tiền đã kiếm đư<PERSON>", "example": 5000.0}, "availableBalance": {"type": "number", "format": "float", "description": "Số dư hiện tại còn lại", "example": 2000.0}, "accountType": {"type": "string", "enum": ["PERSONAL", "BUSINESS"], "description": "<PERSON><PERSON><PERSON> tà<PERSON>", "example": "PERSONAL"}, "referralCode": {"type": "string", "description": "<PERSON>ã giới thiệu", "example": "REF123456"}, "createdAt": {"type": "string", "format": "date-time", "description": "<PERSON><PERSON><PERSON><PERSON> gian tạo", "example": "2024-01-15T10:30:00Z"}}, "required": ["id", "status", "totalEarned", "availableBalance", "accountType", "createdAt"]}, "AffiliateWithdrawalDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "<PERSON> của yêu cầu rút tiền", "example": 1}, "amount": {"type": "number", "format": "float", "description": "<PERSON><PERSON> tiền yêu cầu r<PERSON>t", "example": 1000.0}, "status": {"type": "string", "enum": ["PENDING", "APPROVED", "REJECTED", "COMPLETED"], "description": "<PERSON>r<PERSON><PERSON> thái yêu cầu rút tiền", "example": "PENDING"}, "bankAccount": {"type": "string", "description": "Số tài k<PERSON>n ngân hàng", "example": "**********"}, "bankName": {"type": "string", "description": "<PERSON><PERSON><PERSON> ng<PERSON> hàng", "example": "Vietcombank"}, "accountHolderName": {"type": "string", "description": "<PERSON>ên chủ tài k<PERSON>n", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "requestedAt": {"type": "string", "format": "date-time", "description": "<PERSON><PERSON><PERSON><PERSON> gian yêu c<PERSON>u", "example": "2024-01-15T10:30:00Z"}, "processedAt": {"type": "string", "format": "date-time", "description": "<PERSON><PERSON><PERSON><PERSON> gian xử lý", "example": "2024-01-16T14:20:00Z", "nullable": true}}, "required": ["id", "amount", "status", "bankAccount", "bankName", "accountHolderName", "requestedAt"]}, "CreateWithdrawRequestDto": {"type": "object", "properties": {"amount": {"type": "number", "format": "float", "minimum": 100000, "description": "<PERSON><PERSON> tiền yêu cầu rú<PERSON> (tối thiểu 100,000 VND)", "example": 1000000.0}, "bankAccount": {"type": "string", "description": "Số tài k<PERSON>n ngân hàng", "example": "**********"}, "bankName": {"type": "string", "description": "<PERSON><PERSON><PERSON> ng<PERSON> hàng", "example": "Vietcombank"}, "accountHolderName": {"type": "string", "description": "<PERSON>ên chủ tài k<PERSON>n", "example": "<PERSON><PERSON><PERSON><PERSON>"}}, "required": ["amount", "bankAccount", "bankName", "accountHolderName"]}, "WithdrawResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "<PERSON> của yêu cầu rút tiền", "example": 1}, "amount": {"type": "number", "format": "float", "description": "<PERSON><PERSON> tiền yêu cầu r<PERSON>t", "example": 1000000.0}, "status": {"type": "string", "enum": ["PENDING"], "description": "<PERSON>r<PERSON><PERSON> thái yêu cầu rút tiền", "example": "PENDING"}, "requestedAt": {"type": "string", "format": "date-time", "description": "<PERSON><PERSON><PERSON><PERSON> gian yêu c<PERSON>u", "example": "2024-01-15T10:30:00Z"}}, "required": ["id", "amount", "status", "requestedAt"]}, "PaginationMeta": {"type": "object", "properties": {"page": {"type": "integer", "description": "<PERSON><PERSON> hi<PERSON>n tại", "example": 1}, "limit": {"type": "integer", "description": "Số lượng item mỗi trang", "example": 10}, "total": {"type": "integer", "description": "Tổng số item", "example": 100}, "totalPages": {"type": "integer", "description": "Tổng số trang", "example": 10}}, "required": ["page", "limit", "total", "totalPages"]}}, "responses": {"BadRequest": {"description": "<PERSON><PERSON><PERSON> c<PERSON>u kh<PERSON>ng h<PERSON>p lệ", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Validation failed"}, "errorCode": {"type": "integer", "example": 400}}}}}}, "Unauthorized": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Unauthorized"}, "errorCode": {"type": "integer", "example": 401}}}}}}, "Forbidden": {"description": "<PERSON><PERSON> cấm truy cập", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Forbidden"}, "errorCode": {"type": "integer", "example": 403}}}}}}, "NotFound": {"description": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tài nguyên", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Resource not found"}, "errorCode": {"type": "integer", "example": 404}}}}}}, "InternalServerError": {"description": "Lỗi máy chủ nội bộ", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Internal server error"}, "errorCode": {"type": "integer", "example": 500}}}}}}}}}