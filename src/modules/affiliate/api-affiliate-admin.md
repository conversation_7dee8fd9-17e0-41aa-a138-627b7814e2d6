# Affiliate Admin API Documentation

## Tổng Quan

Tài liệu này mô tả chi tiết các API dành cho admin để quản lý hệ thống affiliate, bao gồm quản lý tà<PERSON>, l<PERSON><PERSON><PERSON> click, r<PERSON><PERSON> tiề<PERSON>, c<PERSON><PERSON> bậc, chuy<PERSON>n đổi điểm và hợp đồng.

## Base URL
```
https://api.redai.com
```

## Authentication
Tất cả APIs yêu cầu JWT token của employee trong header:
```
Authorization: Bearer <employee_jwt_token>
```

---

## 1. Affiliate Overview APIs

### GET /admin/affiliate/overview
Lấy thông tin tổng quan về hệ thống affiliate.

**Headers:**
- `Authorization: Bearer <token>` (required)

**Response 200:**
```json
{
  "success": true,
  "message": "Lấy thông tin tổng quan thành công",
  "data": {
    "totalPublishers": 150,
    "totalRanks": 5,
    "totalOrders": 1250,
    "totalPointConversions": 85,
    "totalRevenue": 125000.50,
    "totalCommission": 12500.05,
    "totalClicks": 5000,
    "averageConversionRate": 25.0
  }
}
```

**Error Responses:**
- `401`: Unauthorized
- `500`: Internal Server Error

---

## 2. Affiliate Account Management APIs

### GET /admin/affiliate/accounts
Lấy danh sách tài khoản affiliate với phân trang và lọc.

**Headers:**
- `Authorization: Bearer <token>` (required)

**Query Parameters:**
- `page` (integer, optional): Số trang, mặc định 1
- `limit` (integer, optional): Số lượng kết quả mỗi trang, mặc định 10, tối đa 100
- `search` (string, optional): Từ khóa tìm kiếm theo tên hoặc email
- `status` (string, optional): Lọc theo trạng thái
  - Enum: `DRAFT`, `PENDING_APPROVAL`, `APPROVED`, `REJECTED`, `ACTIVE`, `INACTIVE`
- `sortBy` (string, optional): Trường sắp xếp
  - Enum: `createdAt`, `updatedAt`, `totalEarned`, `availableBalance`
  - Mặc định: `createdAt`
- `sortDirection` (string, optional): Hướng sắp xếp
  - Enum: `ASC`, `DESC`
  - Mặc định: `DESC`

**Response 200:**
```json
{
  "success": true,
  "message": "Lấy danh sách tài khoản affiliate thành công",
  "data": {
    "items": [
      {
        "id": 1,
        "userId": 123,
        "status": "ACTIVE",
        "totalEarned": 5000.00,
        "totalPaidOut": 3000.00,
        "availableBalance": 2000.00,
        "performance": 25,
        "accountType": "PERSONAL",
        "createdAt": "2024-01-15T10:30:00Z",
        "updatedAt": "2024-01-20T15:45:00Z",
        "user": {
          "id": 123,
          "email": "<EMAIL>",
          "fullName": "Nguyễn Văn A"
        }
      }
    ],
    "meta": {
      "page": 1,
      "limit": 10,
      "total": 100,
      "totalPages": 10
    }
  }
}
```

**Error Responses:**
- `400`: Bad Request - Tham số không hợp lệ
- `401`: Unauthorized
- `500`: Internal Server Error

### GET /admin/affiliate/accounts/{id}
Lấy thông tin chi tiết tài khoản affiliate theo ID.

**Headers:**
- `Authorization: Bearer <token>` (required)

**Path Parameters:**
- `id` (integer, required): ID của tài khoản affiliate

**Response 200:**
```json
{
  "success": true,
  "message": "Lấy thông tin tài khoản affiliate thành công",
  "data": {
    "id": 1,
    "userId": 123,
    "status": "ACTIVE",
    "totalEarned": 5000.00,
    "totalPaidOut": 3000.00,
    "availableBalance": 2000.00,
    "performance": 25,
    "accountType": "PERSONAL",
    "createdAt": "2024-01-15T10:30:00Z",
    "updatedAt": "2024-01-20T15:45:00Z",
    "user": {
      "id": 123,
      "email": "<EMAIL>",
      "fullName": "Nguyễn Văn A"
    }
  }
}
```

**Error Responses:**
- `401`: Unauthorized
- `404`: Not Found - Không tìm thấy tài khoản affiliate
- `500`: Internal Server Error

### PATCH /admin/affiliate/accounts/{id}
Cập nhật trạng thái tài khoản affiliate.

**Headers:**
- `Authorization: Bearer <token>` (required)
- `Content-Type: application/json`

**Path Parameters:**
- `id` (integer, required): ID của tài khoản affiliate

**Request Body:**
```json
{
  "status": "APPROVED"
}
```

**Body Parameters:**
- `status` (string, required): Trạng thái mới
  - Enum: `DRAFT`, `PENDING_APPROVAL`, `APPROVED`, `REJECTED`, `ACTIVE`, `INACTIVE`

**Response 200:**
```json
{
  "success": true,
  "message": "Cập nhật trạng thái tài khoản affiliate thành công"
}
```

**Error Responses:**
- `400`: Bad Request - Dữ liệu không hợp lệ
- `401`: Unauthorized
- `404`: Not Found - Không tìm thấy tài khoản affiliate
- `500`: Internal Server Error

---

## 3. Affiliate Click Management APIs

### GET /admin/affiliate/clicks
Lấy danh sách lượt click với phân trang và lọc.

**Headers:**
- `Authorization: Bearer <token>` (required)

**Query Parameters:**
- `page` (integer, optional): Số trang, mặc định 1
- `limit` (integer, optional): Số lượng kết quả mỗi trang, mặc định 10, tối đa 100
- `startDate` (string, optional): Ngày bắt đầu lọc (YYYY-MM-DD)
- `endDate` (string, optional): Ngày kết thúc lọc (YYYY-MM-DD)

**Response 200:**
```json
{
  "success": true,
  "message": "Lấy danh sách lượt click thành công",
  "data": {
    "items": [
      {
        "id": 1,
        "affiliateAccountId": 1,
        "ipAddress": "***********",
        "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        "referrer": "https://example.com/page",
        "clickedAt": "2024-01-15T10:30:00Z"
      }
    ],
    "meta": {
      "page": 1,
      "limit": 10,
      "total": 100,
      "totalPages": 10
    }
  }
}
```

**Error Responses:**
- `400`: Bad Request - Tham số không hợp lệ
- `401`: Unauthorized
- `500`: Internal Server Error

### GET /admin/affiliate/clicks/statistics
Lấy thống kê lượt click.

**Headers:**
- `Authorization: Bearer <token>` (required)

**Response 200:**
```json
{
  "success": true,
  "message": "Lấy thống kê lượt click thành công",
  "data": {
    "totalClicks": 5000,
    "todayClicks": 150,
    "thisWeekClicks": 800,
    "thisMonthClicks": 2500,
    "clicksByDate": [
      {
        "date": "2024-01-15",
        "clicks": 120
      }
    ],
    "topReferrers": [
      {
        "referrer": "https://facebook.com",
        "clicks": 500
      }
    ]
  }
}
```

**Error Responses:**
- `401`: Unauthorized
- `500`: Internal Server Error

---

## 4. Affiliate Withdrawal Management APIs

### GET /admin/affiliate/withdrawals
Lấy danh sách yêu cầu rút tiền với phân trang và lọc.

**Headers:**
- `Authorization: Bearer <token>` (required)

**Query Parameters:**
- `page` (integer, optional): Số trang, mặc định 1
- `limit` (integer, optional): Số lượng kết quả mỗi trang, mặc định 10, tối đa 100
- `status` (string, optional): Lọc theo trạng thái
  - Enum: `PENDING`, `APPROVED`, `REJECTED`, `COMPLETED`
- `startDate` (string, optional): Ngày bắt đầu lọc (YYYY-MM-DD)
- `endDate` (string, optional): Ngày kết thúc lọc (YYYY-MM-DD)
- `sortBy` (string, optional): Trường sắp xếp
  - Enum: `requestedAt`, `amount`, `status`
  - Mặc định: `requestedAt`
- `sortDirection` (string, optional): Hướng sắp xếp
  - Enum: `ASC`, `DESC`
  - Mặc định: `DESC`

**Response 200:**
```json
{
  "success": true,
  "message": "Lấy danh sách yêu cầu rút tiền thành công",
  "data": {
    "items": [
      {
        "id": 1,
        "affiliateAccountId": 1,
        "amount": 1000000.00,
        "status": "PENDING",
        "bankAccount": "**********",
        "bankName": "Vietcombank",
        "accountHolderName": "Nguyễn Văn A",
        "requestedAt": "2024-01-15T10:30:00Z",
        "processedAt": null,
        "affiliateAccount": {
          "id": 1,
          "user": {
            "id": 123,
            "email": "<EMAIL>",
            "fullName": "Nguyễn Văn A"
          }
        }
      }
    ],
    "meta": {
      "page": 1,
      "limit": 10,
      "total": 50,
      "totalPages": 5
    }
  }
}
```

**Error Responses:**
- `400`: Bad Request - Tham số không hợp lệ
- `401`: Unauthorized
- `500`: Internal Server Error

### GET /admin/affiliate/withdrawals/{id}
Lấy thông tin chi tiết yêu cầu rút tiền theo ID.

**Headers:**
- `Authorization: Bearer <token>` (required)

**Path Parameters:**
- `id` (integer, required): ID của yêu cầu rút tiền

**Response 200:**
```json
{
  "success": true,
  "message": "Lấy thông tin yêu cầu rút tiền thành công",
  "data": {
    "id": 1,
    "affiliateAccountId": 1,
    "amount": 1000000.00,
    "status": "PENDING",
    "bankAccount": "**********",
    "bankName": "Vietcombank",
    "accountHolderName": "Nguyễn Văn A",
    "requestedAt": "2024-01-15T10:30:00Z",
    "processedAt": null,
    "notes": "Yêu cầu rút tiền hàng tháng",
    "affiliateAccount": {
      "id": 1,
      "user": {
        "id": 123,
        "email": "<EMAIL>",
        "fullName": "Nguyễn Văn A"
      }
    }
  }
}
```

**Error Responses:**
- `401`: Unauthorized
- `404`: Not Found - Không tìm thấy yêu cầu rút tiền
- `500`: Internal Server Error

### PATCH /admin/affiliate/withdrawals/{id}
Cập nhật trạng thái yêu cầu rút tiền.

**Headers:**
- `Authorization: Bearer <token>` (required)
- `Content-Type: application/json`

**Path Parameters:**
- `id` (integer, required): ID của yêu cầu rút tiền

**Request Body:**
```json
{
  "status": "APPROVED",
  "notes": "Đã xử lý và chuyển khoản thành công"
}
```

**Body Parameters:**
- `status` (string, required): Trạng thái mới
  - Enum: `PENDING`, `APPROVED`, `REJECTED`, `COMPLETED`
- `notes` (string, optional): Ghi chú từ admin

**Response 200:**
```json
{
  "success": true,
  "message": "Cập nhật trạng thái yêu cầu rút tiền thành công"
}
```

**Error Responses:**
- `400`: Bad Request - Dữ liệu không hợp lệ
- `401`: Unauthorized
- `404`: Not Found - Không tìm thấy yêu cầu rút tiền
- `500`: Internal Server Error

---

## 5. Affiliate Rank Management APIs

### GET /admin/affiliate/ranks
Lấy danh sách cấp bậc affiliate với phân trang.

**Headers:**
- `Authorization: Bearer <token>` (required)

**Query Parameters:**
- `page` (integer, optional): Số trang, mặc định 1
- `limit` (integer, optional): Số lượng kết quả mỗi trang, mặc định 10, tối đa 100
- `search` (string, optional): Từ khóa tìm kiếm theo tên cấp bậc
- `isActive` (boolean, optional): Lọc theo trạng thái kích hoạt
- `sortBy` (string, optional): Trường sắp xếp
  - Enum: `name`, `minPerformance`, `commissionRate`, `createdAt`
  - Mặc định: `createdAt`
- `sortDirection` (string, optional): Hướng sắp xếp
  - Enum: `ASC`, `DESC`
  - Mặc định: `DESC`

**Response 200:**
```json
{
  "success": true,
  "message": "Lấy danh sách cấp bậc affiliate thành công",
  "data": {
    "items": [
      {
        "id": 1,
        "name": "Bronze",
        "description": "Cấp bậc đồng",
        "minPerformance": 0,
        "maxPerformance": 10,
        "commissionRate": 5.0,
        "isActive": true,
        "createdAt": "2024-01-15T10:30:00Z",
        "updatedAt": "2024-01-20T15:45:00Z"
      }
    ],
    "meta": {
      "page": 1,
      "limit": 10,
      "total": 5,
      "totalPages": 1
    }
  }
}
```

**Error Responses:**
- `400`: Bad Request - Tham số không hợp lệ
- `401`: Unauthorized
- `500`: Internal Server Error

### POST /admin/affiliate/ranks
Tạo cấp bậc affiliate mới.

**Headers:**
- `Authorization: Bearer <token>` (required)
- `Content-Type: application/json`

**Request Body:**
```json
{
  "name": "Gold",
  "description": "Cấp bậc vàng",
  "minPerformance": 21,
  "maxPerformance": 50,
  "commissionRate": 10.0,
  "isActive": true
}
```

**Body Parameters:**
- `name` (string, required): Tên cấp bậc (duy nhất)
- `description` (string, optional): Mô tả cấp bậc
- `minPerformance` (integer, required): Hiệu suất tối thiểu (>= 0)
- `maxPerformance` (integer, optional): Hiệu suất tối đa
- `commissionRate` (number, required): Tỷ lệ hoa hồng (0-100)
- `isActive` (boolean, optional): Trạng thái kích hoạt, mặc định true

**Response 201:**
```json
{
  "success": true,
  "message": "Tạo cấp bậc affiliate thành công",
  "data": {
    "id": 6,
    "name": "Gold",
    "description": "Cấp bậc vàng",
    "minPerformance": 21,
    "maxPerformance": 50,
    "commissionRate": 10.0,
    "isActive": true,
    "createdAt": "2024-01-25T10:30:00Z",
    "updatedAt": "2024-01-25T10:30:00Z"
  }
}
```

**Error Responses:**
- `400`: Bad Request - Dữ liệu không hợp lệ hoặc tên đã tồn tại
- `401`: Unauthorized
- `500`: Internal Server Error

### GET /admin/affiliate/ranks/{id}
Lấy thông tin chi tiết cấp bậc affiliate theo ID.

**Headers:**
- `Authorization: Bearer <token>` (required)

**Path Parameters:**
- `id` (integer, required): ID của cấp bậc affiliate

**Response 200:**
```json
{
  "success": true,
  "message": "Lấy thông tin cấp bậc affiliate thành công",
  "data": {
    "id": 1,
    "name": "Bronze",
    "description": "Cấp bậc đồng",
    "minPerformance": 0,
    "maxPerformance": 10,
    "commissionRate": 5.0,
    "isActive": true,
    "createdAt": "2024-01-15T10:30:00Z",
    "updatedAt": "2024-01-20T15:45:00Z",
    "statistics": {
      "totalAccounts": 25,
      "totalEarned": 50000.00,
      "averagePerformance": 7.5
    }
  }
}
```

**Error Responses:**
- `401`: Unauthorized
- `404`: Not Found - Không tìm thấy cấp bậc affiliate
- `500`: Internal Server Error

### PATCH /admin/affiliate/ranks/{id}
Cập nhật thông tin cấp bậc affiliate.

**Headers:**
- `Authorization: Bearer <token>` (required)
- `Content-Type: application/json`

**Path Parameters:**
- `id` (integer, required): ID của cấp bậc affiliate

**Request Body:**
```json
{
  "name": "Bronze Plus",
  "description": "Cấp bậc đồng nâng cao",
  "minPerformance": 0,
  "maxPerformance": 15,
  "commissionRate": 6.0,
  "isActive": true
}
```

**Body Parameters:**
- `name` (string, optional): Tên cấp bậc (duy nhất)
- `description` (string, optional): Mô tả cấp bậc
- `minPerformance` (integer, optional): Hiệu suất tối thiểu (>= 0)
- `maxPerformance` (integer, optional): Hiệu suất tối đa
- `commissionRate` (number, optional): Tỷ lệ hoa hồng (0-100)
- `isActive` (boolean, optional): Trạng thái kích hoạt

**Response 200:**
```json
{
  "success": true,
  "message": "Cập nhật cấp bậc affiliate thành công"
}
```

**Error Responses:**
- `400`: Bad Request - Dữ liệu không hợp lệ
- `401`: Unauthorized
- `404`: Not Found - Không tìm thấy cấp bậc affiliate
- `500`: Internal Server Error

### DELETE /admin/affiliate/ranks/{id}
Xóa cấp bậc affiliate.

**Headers:**
- `Authorization: Bearer <token>` (required)

**Path Parameters:**
- `id` (integer, required): ID của cấp bậc affiliate

**Response 200:**
```json
{
  "success": true,
  "message": "Xóa cấp bậc affiliate thành công"
}
```

**Error Responses:**
- `400`: Bad Request - Cấp bậc đang được sử dụng, không thể xóa
- `401`: Unauthorized
- `404`: Not Found - Không tìm thấy cấp bậc affiliate
- `500`: Internal Server Error

---

## 6. Affiliate Point Conversion Management APIs

### GET /admin/affiliate/point-conversions
Lấy danh sách chuyển đổi điểm affiliate với phân trang.

**Headers:**
- `Authorization: Bearer <token>` (required)

**Query Parameters:**
- `page` (integer, optional): Số trang, mặc định 1
- `limit` (integer, optional): Số lượng kết quả mỗi trang, mặc định 10, tối đa 100
- `startDate` (string, optional): Ngày bắt đầu lọc (YYYY-MM-DD)
- `endDate` (string, optional): Ngày kết thúc lọc (YYYY-MM-DD)
- `sortBy` (string, optional): Trường sắp xếp
  - Enum: `convertedAt`, `pointsConverted`, `conversionRate`
  - Mặc định: `convertedAt`
- `sortDirection` (string, optional): Hướng sắp xếp
  - Enum: `ASC`, `DESC`
  - Mặc định: `DESC`

**Response 200:**
```json
{
  "success": true,
  "message": "Lấy danh sách chuyển đổi điểm thành công",
  "data": {
    "items": [
      {
        "id": 1,
        "affiliateAccountId": 1,
        "pointsConverted": 1000,
        "conversionRate": 1000,
        "amountConverted": 1000000.00,
        "convertedAt": "2024-01-15T10:30:00Z",
        "affiliateAccount": {
          "id": 1,
          "user": {
            "id": 123,
            "email": "<EMAIL>",
            "fullName": "Nguyễn Văn A"
          }
        }
      }
    ],
    "meta": {
      "page": 1,
      "limit": 10,
      "total": 85,
      "totalPages": 9
    }
  }
}
```

**Error Responses:**
- `400`: Bad Request - Tham số không hợp lệ
- `401`: Unauthorized
- `500`: Internal Server Error
