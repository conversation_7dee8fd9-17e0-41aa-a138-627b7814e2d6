# Affiliate Module API Documentation

## Tổng Quan

Module Affiliate cung cấp hệ thống quản lý affiliate marketing hoàn chỉnh, bao gồm quản lý tài khoản affiliate, theo d<PERSON><PERSON> lư<PERSON> click, xử lý đơn hàng, tính toán hoa hồng và quản lý rút tiền.

## Cấu Trúc <PERSON>dule

```
src/modules/affiliate/
├── admin/                          # Admin APIs
│   ├── controllers/                # Admin controllers
│   ├── services/                   # Admin services
│   ├── dto/                        # Admin DTOs
│   └── affiliate-admin.module.ts   # Admin module
├── user/                           # User APIs
│   ├── controllers/                # User controllers
│   ├── services/                   # User services
│   ├── dto/                        # User DTOs
│   └── affiliate-user.module.ts    # User module
├── entities/                       # Database entities
├── repositories/                   # Custom repositories
├── services/                       # Shared services
├── state-machine/                  # Registration state machine
├── errors/                         # Error definitions
├── dto/                           # Shared DTOs
├── swagger.json                   # Swagger documentation
└── affiliate.module.ts            # Main module
```

## Entities

### 1. AffiliateAccount
- **Bảng**: `affiliate_accounts`
- **<PERSON><PERSON> tả**: Thông tin tài khoản affiliate của người dùng
- **Các trường chính**:
  - `id`: ID tài khoản affiliate
  - `userId`: ID người dùng
  - `status`: Trạng thái tài khoản (DRAFT, PENDING_APPROVAL, APPROVED, REJECTED, ACTIVE, INACTIVE)
  - `totalEarned`: Tổng số tiền đã kiếm được
  - `totalPaidOut`: Tổng số tiền đã được thanh toán
  - `availableBalance`: Số dư hiện tại còn lại
  - `performance`: Số lượng khách hàng giới thiệu trong 3 tháng gần nhất
  - `accountType`: Loại tài khoản (PERSONAL, BUSINESS)

### 2. AffiliateClick
- **Bảng**: `affiliate_clicks`
- **Mô tả**: Theo dõi lượt click vào liên kết affiliate
- **Các trường chính**:
  - `id`: ID lượt click
  - `affiliateAccountId`: ID tài khoản affiliate
  - `ipAddress`: Địa chỉ IP của người click
  - `userAgent`: User agent của trình duyệt
  - `referrer`: URL trang giới thiệu
  - `clickedAt`: Thời gian click

### 3. AffiliateCustomerOrder
- **Bảng**: `affiliate_customer_order`
- **Mô tả**: Đơn hàng affiliate
- **Các trường chính**:
  - `orderId`: Mã đơn hàng
  - `commission`: Phần trăm hoa hồng
  - `affiliateAccountId`: ID tài khoản affiliate
  - `rankId`: Mã rank affiliate

### 4. AffiliateWithdrawHistory
- **Bảng**: `affiliate_withdraw_history`
- **Mô tả**: Lịch sử rút tiền affiliate
- **Các trường chính**:
  - `id`: ID yêu cầu rút tiền
  - `affiliateAccountId`: ID tài khoản affiliate
  - `amount`: Số tiền rút
  - `status`: Trạng thái (PENDING, APPROVED, REJECTED, COMPLETED)
  - `bankAccount`: Số tài khoản ngân hàng
  - `bankName`: Tên ngân hàng
  - `accountHolderName`: Tên chủ tài khoản

### 5. AffiliateRank
- **Bảng**: `affiliate_ranks`
- **Mô tả**: Cấp bậc affiliate
- **Các trường chính**:
  - `id`: ID cấp bậc
  - `name`: Tên cấp bậc
  - `minPerformance`: Hiệu suất tối thiểu
  - `commissionRate`: Tỷ lệ hoa hồng
  - `isActive`: Trạng thái kích hoạt

### 6. AffiliatePointConversionHistory
- **Bảng**: `affiliate_point_conversion_history`
- **Mô tả**: Lịch sử chuyển đổi điểm affiliate
- **Các trường chính**:
  - `id`: ID chuyển đổi
  - `affiliateAccountId`: ID tài khoản affiliate
  - `pointsConverted`: Số điểm đã chuyển đổi
  - `conversionRate`: Tỷ lệ chuyển đổi
  - `convertedAt`: Thời gian chuyển đổi

### 7. AffiliateContract
- **Bảng**: `affiliate_contracts`
- **Mô tả**: Hợp đồng affiliate
- **Các trường chính**:
  - `id`: ID hợp đồng
  - `affiliateAccountId`: ID tài khoản affiliate
  - `contractType`: Loại hợp đồng
  - `status`: Trạng thái hợp đồng
  - `signedAt`: Thời gian ký

## API Endpoints

### Admin APIs

#### 1. Affiliate Overview
- **GET** `/admin/affiliate/overview` - Lấy thông tin tổng quan affiliate

#### 2. Affiliate Account Management
- **GET** `/admin/affiliate/accounts` - Lấy danh sách tài khoản affiliate
- **GET** `/admin/affiliate/accounts/{id}` - Lấy chi tiết tài khoản affiliate
- **PATCH** `/admin/affiliate/accounts/{id}` - Cập nhật trạng thái tài khoản

#### 3. Affiliate Click Management
- **GET** `/admin/affiliate/clicks` - Lấy danh sách lượt click
- **GET** `/admin/affiliate/clicks/statistics` - Lấy thống kê lượt click

#### 4. Affiliate Withdrawal Management
- **GET** `/admin/affiliate/withdrawals` - Lấy danh sách yêu cầu rút tiền
- **GET** `/admin/affiliate/withdrawals/{id}` - Lấy chi tiết yêu cầu rút tiền
- **PATCH** `/admin/affiliate/withdrawals/{id}` - Cập nhật trạng thái rút tiền

#### 5. Affiliate Rank Management
- **GET** `/admin/affiliate/ranks` - Lấy danh sách cấp bậc
- **POST** `/admin/affiliate/ranks` - Tạo cấp bậc mới
- **GET** `/admin/affiliate/ranks/{id}` - Lấy chi tiết cấp bậc
- **PATCH** `/admin/affiliate/ranks/{id}` - Cập nhật cấp bậc
- **DELETE** `/admin/affiliate/ranks/{id}` - Xóa cấp bậc

#### 6. Affiliate Point Conversion Management
- **GET** `/admin/affiliate/point-conversions` - Lấy danh sách chuyển đổi điểm

#### 7. Affiliate Contract Management
- **GET** `/admin/affiliate/contracts` - Lấy danh sách hợp đồng
- **GET** `/admin/affiliate/contracts/{id}` - Lấy chi tiết hợp đồng
- **PATCH** `/admin/affiliate/contracts/{id}` - Cập nhật trạng thái hợp đồng

### User APIs

#### 1. Affiliate Statistics
- **GET** `/user/affiliate/statistics` - Lấy thống kê tài khoản affiliate

#### 2. Affiliate Account
- **GET** `/user/affiliate/account` - Lấy thông tin tài khoản affiliate

#### 3. Affiliate Orders
- **GET** `/user/affiliate/orders` - Lấy danh sách đơn hàng affiliate

#### 4. Affiliate Withdrawals
- **GET** `/user/affiliate/withdrawals` - Lấy danh sách yêu cầu rút tiền
- **POST** `/user/affiliate/withdrawals` - Tạo yêu cầu rút tiền mới

#### 5. Affiliate Customers
- **GET** `/user/affiliate/customers` - Lấy danh sách khách hàng giới thiệu

#### 6. Affiliate Point Conversions
- **GET** `/user/affiliate/point-conversions` - Lấy danh sách chuyển đổi điểm
- **POST** `/user/affiliate/point-conversions` - Tạo yêu cầu chuyển đổi điểm

#### 7. Affiliate Referral Links
- **GET** `/user/affiliate/referral-links` - Lấy danh sách liên kết giới thiệu

#### 8. Affiliate Upload
- **POST** `/user/affiliate/upload/business-license` - Upload giấy phép kinh doanh
- **POST** `/user/affiliate/upload/signed-contract` - Upload hợp đồng đã ký

#### 9. Affiliate Business
- **GET** `/user/affiliate/business` - Lấy thông tin doanh nghiệp
- **POST** `/user/affiliate/business` - Tạo thông tin doanh nghiệp
- **PATCH** `/user/affiliate/business` - Cập nhật thông tin doanh nghiệp

#### 10. Affiliate Registration (State Machine)
- **GET** `/user/affiliate/registration/status` - Lấy trạng thái đăng ký
- **POST** `/user/affiliate/registration/start` - Bắt đầu quy trình đăng ký
- **POST** `/user/affiliate/registration/submit-personal` - Gửi thông tin cá nhân
- **POST** `/user/affiliate/registration/submit-business` - Gửi thông tin doanh nghiệp

## Error Codes

Module Affiliate sử dụng các mã lỗi từ 11000-11099:

### Tài Khoản Affiliate (11000-11009)
- `11000`: Không tìm thấy tài khoản affiliate
- `11001`: Tài khoản affiliate chưa được kích hoạt
- `11002`: Lỗi khi cập nhật trạng thái tài khoản affiliate

### Rút Tiền (11010-11019)
- `11010`: Không tìm thấy yêu cầu rút tiền
- `11011`: Số dư không đủ để rút tiền
- `11012`: Số tiền rút không hợp lệ
- `11013`: Lỗi khi tạo yêu cầu rút tiền
- `11014`: Lỗi khi cập nhật trạng thái rút tiền

### Cấp Bậc (11020-11029)
- `11020`: Không tìm thấy cấp bậc affiliate
- `11021`: Tên cấp bậc đã tồn tại
- `11022`: Lỗi khi tạo cấp bậc affiliate
- `11023`: Lỗi khi cập nhật cấp bậc affiliate
- `11024`: Cấp bậc đang được sử dụng, không thể xóa
- `11025`: Lỗi khi xóa cấp bậc affiliate

### Đơn Hàng (11030-11039)
- `11030`: Lỗi khi tạo đơn hàng affiliate
- `11031`: Lỗi khi xử lý hoa hồng affiliate

### Thống Kê và Truy Vấn (11040-11049)
- `11040`: Lỗi khi lấy thông tin thống kê affiliate
- `11041`: Lỗi khi lấy danh sách đơn hàng affiliate
- `11042`: Lỗi khi lấy danh sách khách hàng affiliate

## Authentication & Authorization

- **Admin APIs**: Sử dụng `JwtEmployeeGuard` - Yêu cầu token JWT của nhân viên
- **User APIs**: Sử dụng `JwtUserGuard` - Yêu cầu token JWT của người dùng
- **Bearer Token**: Tất cả APIs yêu cầu header `Authorization: Bearer <token>`

## Response Format

Tất cả APIs đều trả về response theo format chuẩn:

```json
{
  "success": true,
  "message": "Thông báo thành công",
  "data": {
    // Dữ liệu response
  }
}
```

Đối với APIs có phân trang:

```json
{
  "success": true,
  "message": "Thông báo thành công",
  "data": {
    "items": [...],
    "meta": {
      "page": 1,
      "limit": 10,
      "total": 100,
      "totalPages": 10
    }
  }
}
```

## Validation Rules

### Rút Tiền
- Số tiền tối thiểu: 100,000 VND
- Số tài khoản ngân hàng: Bắt buộc
- Tên ngân hàng: Bắt buộc
- Tên chủ tài khoản: Bắt buộc

### Cấp Bậc
- Tên cấp bậc: Duy nhất, không được trống
- Hiệu suất tối thiểu: Số nguyên >= 0
- Tỷ lệ hoa hồng: Số thực từ 0-100

### Phân Trang
- Page: >= 1
- Limit: 1-100
- Sort direction: ASC hoặc DESC

## State Machine

Module sử dụng State Machine để quản lý quy trình đăng ký affiliate:

### States
1. **IDLE** - Trạng thái ban đầu
2. **PERSONAL_INFO_SUBMITTED** - Đã gửi thông tin cá nhân
3. **BUSINESS_INFO_SUBMITTED** - Đã gửi thông tin doanh nghiệp
4. **PENDING_APPROVAL** - Chờ phê duyệt
5. **APPROVED** - Đã được phê duyệt
6. **REJECTED** - Bị từ chối

### Events
- `SUBMIT_PERSONAL_INFO` - Gửi thông tin cá nhân
- `SUBMIT_BUSINESS_INFO` - Gửi thông tin doanh nghiệp
- `APPROVE` - Phê duyệt
- `REJECT` - Từ chối
- `RESET` - Đặt lại

## Performance Considerations

1. **Indexing**: Các trường thường xuyên query đã được index
2. **Caching**: Thống kê được cache để tăng performance
3. **Pagination**: Tất cả list APIs đều hỗ trợ phân trang
4. **Query Optimization**: Sử dụng QueryBuilder thay vì raw SQL

## Security

1. **Input Validation**: Tất cả input đều được validate
2. **SQL Injection Prevention**: Sử dụng parameterized queries
3. **Authorization**: Kiểm tra quyền truy cập cho từng endpoint
4. **Rate Limiting**: Áp dụng rate limiting cho các APIs quan trọng
