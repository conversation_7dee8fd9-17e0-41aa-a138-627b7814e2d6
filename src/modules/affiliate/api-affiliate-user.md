# Affiliate User API Documentation

## Tổng Quan

Tài liệu này mô tả chi tiết các API dành cho người dùng để quản lý tài khoản affiliate, theo d<PERSON><PERSON> thống kê, quản lý rút tiền và các chức năng liên quan.

## Base URL
```
https://api.redai.com
```

## Authentication
Tất cả APIs yêu cầu JWT token của user trong header:
```
Authorization: Bearer <user_jwt_token>
```

---

## 1. Affiliate Statistics APIs

### GET /user/affiliate/statistics
L<PERSON>y thông tin thống kê tài khoản affiliate của người dùng hiện tại.

**Headers:**
- `Authorization: Bearer <token>` (required)

**Query Parameters:**
- `startDate` (string, optional): <PERSON><PERSON><PERSON> b<PERSON><PERSON> đầ<PERSON> lọ<PERSON> (YYYY-MM-DD)
- `endDate` (string, optional): <PERSON><PERSON><PERSON> k<PERSON><PERSON> thúc lọ<PERSON> (YYYY-MM-DD)

**Response 200:**
```json
{
  "success": true,
  "message": "<PERSON><PERSON>y thông tin thống kê thành công",
  "data": {
    "totalEarned": 5000.00,
    "availableBalance": 2000.00,
    "totalClicks": 150,
    "totalOrders": 25,
    "totalCustomers": 20,
    "conversionRate": 16.67,
    "thisMonthEarned": 500.00
  }
}
```

**Error Responses:**
- `401`: Unauthorized
- `404`: Not Found - Không tìm thấy tài khoản affiliate
- `500`: Internal Server Error

---

## 2. Affiliate Account APIs

### GET /user/affiliate/account
Lấy thông tin tài khoản affiliate của người dùng hiện tại.

**Headers:**
- `Authorization: Bearer <token>` (required)

**Response 200:**
```json
{
  "success": true,
  "message": "Lấy thông tin tài khoản affiliate thành công",
  "data": {
    "id": 1,
    "status": "ACTIVE",
    "totalEarned": 5000.00,
    "availableBalance": 2000.00,
    "accountType": "PERSONAL",
    "referralCode": "REF123456",
    "createdAt": "2024-01-15T10:30:00Z"
  }
}
```

**Error Responses:**
- `401`: Unauthorized
- `404`: Not Found - Không tìm thấy tài khoản affiliate
- `500`: Internal Server Error

---

## 3. Affiliate Orders APIs

### GET /user/affiliate/orders
Lấy danh sách đơn hàng affiliate của người dùng hiện tại.

**Headers:**
- `Authorization: Bearer <token>` (required)

**Query Parameters:**
- `page` (integer, optional): Số trang, mặc định 1
- `limit` (integer, optional): Số lượng kết quả mỗi trang, mặc định 10, tối đa 100
- `startDate` (string, optional): Ngày bắt đầu lọc (YYYY-MM-DD)
- `endDate` (string, optional): Ngày kết thúc lọc (YYYY-MM-DD)
- `sortBy` (string, optional): Trường sắp xếp
  - Enum: `createdAt`, `amount`, `commission`
  - Mặc định: `createdAt`
- `sortDirection` (string, optional): Hướng sắp xếp
  - Enum: `ASC`, `DESC`
  - Mặc định: `DESC`

**Response 200:**
```json
{
  "success": true,
  "message": "Lấy danh sách đơn hàng affiliate thành công",
  "data": {
    "items": [
      {
        "orderId": 12345,
        "amount": 1000000.00,
        "commission": 50000.00,
        "commissionRate": 5.0,
        "status": "COMPLETED",
        "createdAt": "2024-01-15T10:30:00Z",
        "customer": {
          "email": "<EMAIL>",
          "fullName": "Nguyễn Thị B"
        }
      }
    ],
    "meta": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "totalPages": 3
    }
  }
}
```

**Error Responses:**
- `400`: Bad Request - Tham số không hợp lệ
- `401`: Unauthorized
- `500`: Internal Server Error

---

## 4. Affiliate Withdrawals APIs

### GET /user/affiliate/withdrawals
Lấy danh sách yêu cầu rút tiền của người dùng hiện tại.

**Headers:**
- `Authorization: Bearer <token>` (required)

**Query Parameters:**
- `page` (integer, optional): Số trang, mặc định 1
- `limit` (integer, optional): Số lượng kết quả mỗi trang, mặc định 10, tối đa 100
- `status` (string, optional): Lọc theo trạng thái
  - Enum: `PENDING`, `APPROVED`, `REJECTED`, `COMPLETED`

**Response 200:**
```json
{
  "success": true,
  "message": "Lấy danh sách yêu cầu rút tiền thành công",
  "data": {
    "items": [
      {
        "id": 1,
        "amount": 1000000.00,
        "status": "PENDING",
        "bankAccount": "*********0",
        "bankName": "Vietcombank",
        "accountHolderName": "Nguyễn Văn A",
        "requestedAt": "2024-01-15T10:30:00Z",
        "processedAt": null
      }
    ],
    "meta": {
      "page": 1,
      "limit": 10,
      "total": 5,
      "totalPages": 1
    }
  }
}
```

**Error Responses:**
- `401`: Unauthorized
- `500`: Internal Server Error

### POST /user/affiliate/withdrawals
Tạo yêu cầu rút tiền mới.

**Headers:**
- `Authorization: Bearer <token>` (required)
- `Content-Type: application/json`

**Request Body:**
```json
{
  "amount": 1000000.00,
  "bankAccount": "*********0",
  "bankName": "Vietcombank",
  "accountHolderName": "Nguyễn Văn A"
}
```

**Body Parameters:**
- `amount` (number, required): Số tiền yêu cầu rút (tối thiểu 100,000 VND)
- `bankAccount` (string, required): Số tài khoản ngân hàng
- `bankName` (string, required): Tên ngân hàng
- `accountHolderName` (string, required): Tên chủ tài khoản

**Response 201:**
```json
{
  "success": true,
  "message": "Tạo yêu cầu rút tiền thành công",
  "data": {
    "id": 1,
    "amount": 1000000.00,
    "status": "PENDING",
    "requestedAt": "2024-01-15T10:30:00Z"
  }
}
```

**Error Responses:**
- `400`: Bad Request - Dữ liệu không hợp lệ hoặc số dư không đủ
- `401`: Unauthorized
- `500`: Internal Server Error

---

## 5. Affiliate Customers APIs

### GET /user/affiliate/customers
Lấy danh sách khách hàng giới thiệu của người dùng hiện tại.

**Headers:**
- `Authorization: Bearer <token>` (required)

**Query Parameters:**
- `page` (integer, optional): Số trang, mặc định 1
- `limit` (integer, optional): Số lượng kết quả mỗi trang, mặc định 10, tối đa 100
- `search` (string, optional): Từ khóa tìm kiếm theo email hoặc tên
- `startDate` (string, optional): Ngày bắt đầu lọc (YYYY-MM-DD)
- `endDate` (string, optional): Ngày kết thúc lọc (YYYY-MM-DD)
- `sortBy` (string, optional): Trường sắp xếp
  - Enum: `registeredAt`, `totalOrders`, `totalSpent`
  - Mặc định: `registeredAt`
- `sortDirection` (string, optional): Hướng sắp xếp
  - Enum: `ASC`, `DESC`
  - Mặc định: `DESC`

**Response 200:**
```json
{
  "success": true,
  "message": "Lấy danh sách khách hàng affiliate thành công",
  "data": {
    "items": [
      {
        "id": 1,
        "email": "<EMAIL>",
        "fullName": "Nguyễn Thị B",
        "registeredAt": "2024-01-15T10:30:00Z",
        "totalOrders": 3,
        "totalSpent": 2500000.00,
        "totalCommission": 125000.00,
        "lastOrderAt": "2024-01-20T15:45:00Z"
      }
    ],
    "meta": {
      "page": 1,
      "limit": 10,
      "total": 20,
      "totalPages": 2
    }
  }
}
```

**Error Responses:**
- `400`: Bad Request - Tham số không hợp lệ
- `401`: Unauthorized
- `500`: Internal Server Error

---

## 6. Affiliate Point Conversions APIs

### GET /user/affiliate/point-conversions
Lấy danh sách chuyển đổi điểm của người dùng hiện tại.

**Headers:**
- `Authorization: Bearer <token>` (required)

**Query Parameters:**
- `page` (integer, optional): Số trang, mặc định 1
- `limit` (integer, optional): Số lượng kết quả mỗi trang, mặc định 10, tối đa 100
- `startDate` (string, optional): Ngày bắt đầu lọc (YYYY-MM-DD)
- `endDate` (string, optional): Ngày kết thúc lọc (YYYY-MM-DD)

**Response 200:**
```json
{
  "success": true,
  "message": "Lấy danh sách chuyển đổi điểm thành công",
  "data": {
    "items": [
      {
        "id": 1,
        "pointsConverted": 1000,
        "conversionRate": 1000,
        "amountConverted": 1000000.00,
        "convertedAt": "2024-01-15T10:30:00Z"
      }
    ],
    "meta": {
      "page": 1,
      "limit": 10,
      "total": 5,
      "totalPages": 1
    }
  }
}
```

**Error Responses:**
- `401`: Unauthorized
- `500`: Internal Server Error

### POST /user/affiliate/point-conversions
Tạo yêu cầu chuyển đổi điểm mới.

**Headers:**
- `Authorization: Bearer <token>` (required)
- `Content-Type: application/json`

**Request Body:**
```json
{
  "pointsToConvert": 1000
}
```

**Body Parameters:**
- `pointsToConvert` (integer, required): Số điểm muốn chuyển đổi (tối thiểu 100)

**Response 201:**
```json
{
  "success": true,
  "message": "Tạo yêu cầu chuyển đổi điểm thành công",
  "data": {
    "id": 1,
    "pointsConverted": 1000,
    "conversionRate": 1000,
    "amountConverted": 1000000.00,
    "convertedAt": "2024-01-15T10:30:00Z"
  }
}
```

**Error Responses:**
- `400`: Bad Request - Dữ liệu không hợp lệ hoặc số điểm không đủ
- `401`: Unauthorized
- `500`: Internal Server Error

---

## 7. Affiliate Referral Links APIs

### GET /user/affiliate/referral-links
Lấy danh sách liên kết giới thiệu của người dùng hiện tại.

**Headers:**
- `Authorization: Bearer <token>` (required)

**Response 200:**
```json
{
  "success": true,
  "message": "Lấy danh sách liên kết giới thiệu thành công",
  "data": {
    "referralCode": "REF123456",
    "links": [
      {
        "type": "REGISTRATION",
        "url": "https://redai.com/register?ref=REF123456",
        "description": "Liên kết đăng ký tài khoản"
      },
      {
        "type": "PRODUCT",
        "url": "https://redai.com/products?ref=REF123456",
        "description": "Liên kết sản phẩm"
      },
      {
        "type": "SUBSCRIPTION",
        "url": "https://redai.com/pricing?ref=REF123456",
        "description": "Liên kết gói dịch vụ"
      }
    ],
    "statistics": {
      "totalClicks": 150,
      "totalConversions": 25,
      "conversionRate": 16.67
    }
  }
}
```

**Error Responses:**
- `401`: Unauthorized
- `404`: Not Found - Không tìm thấy tài khoản affiliate
- `500`: Internal Server Error

---

## 8. Affiliate Upload APIs

### POST /user/affiliate/upload/business-license
Upload giấy phép kinh doanh cho tài khoản affiliate doanh nghiệp.

**Headers:**
- `Authorization: Bearer <token>` (required)
- `Content-Type: application/json`

**Request Body:**
```json
{
  "fileName": "business-license.pdf",
  "fileType": "application/pdf"
}
```

**Body Parameters:**
- `fileName` (string, required): Tên file
- `fileType` (string, required): Loại file (application/pdf, image/jpeg, image/png)

**Response 200:**
```json
{
  "success": true,
  "message": "Tạo URL upload thành công",
  "data": {
    "uploadUrl": "https://s3.amazonaws.com/bucket/upload-url",
    "fileKey": "affiliate/business-license/*********.pdf",
    "expiresIn": 3600
  }
}
```

**Error Responses:**
- `400`: Bad Request - Dữ liệu không hợp lệ
- `401`: Unauthorized
- `500`: Internal Server Error

### POST /user/affiliate/upload/signed-contract
Upload hợp đồng đã ký cho tài khoản affiliate.

**Headers:**
- `Authorization: Bearer <token>` (required)
- `Content-Type: application/json`

**Request Body:**
```json
{
  "fileName": "signed-contract.pdf",
  "fileType": "application/pdf"
}
```

**Body Parameters:**
- `fileName` (string, required): Tên file
- `fileType` (string, required): Loại file (application/pdf, image/jpeg, image/png)

**Response 200:**
```json
{
  "success": true,
  "message": "Tạo URL upload thành công",
  "data": {
    "uploadUrl": "https://s3.amazonaws.com/bucket/upload-url",
    "fileKey": "affiliate/signed-contract/*********.pdf",
    "expiresIn": 3600
  }
}
```

**Error Responses:**
- `400`: Bad Request - Dữ liệu không hợp lệ
- `401`: Unauthorized
- `500`: Internal Server Error

---

## 9. Affiliate Business APIs

### GET /user/affiliate/business
Lấy thông tin doanh nghiệp của tài khoản affiliate.

**Headers:**
- `Authorization: Bearer <token>` (required)

**Response 200:**
```json
{
  "success": true,
  "message": "Lấy thông tin doanh nghiệp thành công",
  "data": {
    "id": 1,
    "companyName": "Công ty TNHH ABC",
    "taxCode": "0*********",
    "address": "123 Đường ABC, Quận 1, TP.HCM",
    "phoneNumber": "**********",
    "email": "<EMAIL>",
    "website": "https://abc.com",
    "businessLicenseUrl": "https://s3.amazonaws.com/bucket/business-license.pdf",
    "status": "APPROVED",
    "createdAt": "2024-01-15T10:30:00Z",
    "updatedAt": "2024-01-20T15:45:00Z"
  }
}
```

**Error Responses:**
- `401`: Unauthorized
- `404`: Not Found - Không tìm thấy thông tin doanh nghiệp
- `500`: Internal Server Error

### POST /user/affiliate/business
Tạo thông tin doanh nghiệp cho tài khoản affiliate.

**Headers:**
- `Authorization: Bearer <token>` (required)
- `Content-Type: application/json`

**Request Body:**
```json
{
  "companyName": "Công ty TNHH ABC",
  "taxCode": "0*********",
  "address": "123 Đường ABC, Quận 1, TP.HCM",
  "phoneNumber": "**********",
  "email": "<EMAIL>",
  "website": "https://abc.com",
  "businessLicenseUrl": "https://s3.amazonaws.com/bucket/business-license.pdf"
}
```

**Body Parameters:**
- `companyName` (string, required): Tên công ty
- `taxCode` (string, required): Mã số thuế
- `address` (string, required): Địa chỉ công ty
- `phoneNumber` (string, required): Số điện thoại
- `email` (string, required): Email công ty
- `website` (string, optional): Website công ty
- `businessLicenseUrl` (string, required): URL giấy phép kinh doanh

**Response 201:**
```json
{
  "success": true,
  "message": "Tạo thông tin doanh nghiệp thành công",
  "data": {
    "id": 1,
    "companyName": "Công ty TNHH ABC",
    "taxCode": "0*********",
    "address": "123 Đường ABC, Quận 1, TP.HCM",
    "phoneNumber": "**********",
    "email": "<EMAIL>",
    "website": "https://abc.com",
    "businessLicenseUrl": "https://s3.amazonaws.com/bucket/business-license.pdf",
    "status": "PENDING",
    "createdAt": "2024-01-15T10:30:00Z",
    "updatedAt": "2024-01-15T10:30:00Z"
  }
}
```

**Error Responses:**
- `400`: Bad Request - Dữ liệu không hợp lệ hoặc đã tồn tại thông tin doanh nghiệp
- `401`: Unauthorized
- `500`: Internal Server Error

### PATCH /user/affiliate/business
Cập nhật thông tin doanh nghiệp của tài khoản affiliate.

**Headers:**
- `Authorization: Bearer <token>` (required)
- `Content-Type: application/json`

**Request Body:**
```json
{
  "companyName": "Công ty TNHH ABC Updated",
  "address": "456 Đường XYZ, Quận 2, TP.HCM",
  "phoneNumber": "**********",
  "website": "https://abc-updated.com"
}
```

**Body Parameters:**
- `companyName` (string, optional): Tên công ty
- `taxCode` (string, optional): Mã số thuế
- `address` (string, optional): Địa chỉ công ty
- `phoneNumber` (string, optional): Số điện thoại
- `email` (string, optional): Email công ty
- `website` (string, optional): Website công ty
- `businessLicenseUrl` (string, optional): URL giấy phép kinh doanh

**Response 200:**
```json
{
  "success": true,
  "message": "Cập nhật thông tin doanh nghiệp thành công"
}
```

**Error Responses:**
- `400`: Bad Request - Dữ liệu không hợp lệ
- `401`: Unauthorized
- `404`: Not Found - Không tìm thấy thông tin doanh nghiệp
- `500`: Internal Server Error

---

## 10. Affiliate Registration APIs (State Machine)

### GET /user/affiliate/registration/status
Lấy trạng thái đăng ký affiliate hiện tại.

**Headers:**
- `Authorization: Bearer <token>` (required)

**Response 200:**
```json
{
  "success": true,
  "message": "Lấy trạng thái đăng ký thành công",
  "data": {
    "currentState": "PERSONAL_INFO_SUBMITTED",
    "availableActions": ["SUBMIT_BUSINESS_INFO", "RESET"],
    "context": {
      "personalInfo": {
        "fullName": "Nguyễn Văn A",
        "phoneNumber": "**********",
        "address": "123 Đường ABC"
      },
      "submittedAt": "2024-01-15T10:30:00Z"
    }
  }
}
```

**Error Responses:**
- `401`: Unauthorized
- `500`: Internal Server Error

### POST /user/affiliate/registration/start
Bắt đầu quy trình đăng ký affiliate.

**Headers:**
- `Authorization: Bearer <token>` (required)
- `Content-Type: application/json`

**Request Body:**
```json
{
  "accountType": "PERSONAL"
}
```

**Body Parameters:**
- `accountType` (string, required): Loại tài khoản
  - Enum: `PERSONAL`, `BUSINESS`

**Response 200:**
```json
{
  "success": true,
  "message": "Quy trình đăng ký affiliate đã được khởi tạo",
  "data": {
    "state": "IDLE",
    "context": {
      "accountType": "PERSONAL",
      "startedAt": "2024-01-15T10:30:00Z"
    }
  }
}
```

**Error Responses:**
- `400`: Bad Request - Dữ liệu không hợp lệ hoặc đã có tài khoản affiliate
- `401`: Unauthorized
- `500`: Internal Server Error

### POST /user/affiliate/registration/submit-personal
Gửi thông tin cá nhân trong quy trình đăng ký.

**Headers:**
- `Authorization: Bearer <token>` (required)
- `Content-Type: application/json`

**Request Body:**
```json
{
  "fullName": "Nguyễn Văn A",
  "phoneNumber": "**********",
  "address": "123 Đường ABC, Quận 1, TP.HCM",
  "idNumber": "*********",
  "idCardFrontUrl": "https://s3.amazonaws.com/bucket/id-front.jpg",
  "idCardBackUrl": "https://s3.amazonaws.com/bucket/id-back.jpg"
}
```

**Body Parameters:**
- `fullName` (string, required): Họ và tên
- `phoneNumber` (string, required): Số điện thoại
- `address` (string, required): Địa chỉ
- `idNumber` (string, required): Số CMND/CCCD
- `idCardFrontUrl` (string, required): URL ảnh mặt trước CMND/CCCD
- `idCardBackUrl` (string, required): URL ảnh mặt sau CMND/CCCD

**Response 200:**
```json
{
  "success": true,
  "message": "Gửi thông tin cá nhân thành công",
  "data": {
    "state": "PERSONAL_INFO_SUBMITTED",
    "nextActions": ["SUBMIT_BUSINESS_INFO", "SUBMIT_FOR_APPROVAL"]
  }
}
```

**Error Responses:**
- `400`: Bad Request - Dữ liệu không hợp lệ hoặc trạng thái không cho phép
- `401`: Unauthorized
- `500`: Internal Server Error

### POST /user/affiliate/registration/submit-business
Gửi thông tin doanh nghiệp trong quy trình đăng ký (chỉ cho tài khoản BUSINESS).

**Headers:**
- `Authorization: Bearer <token>` (required)
- `Content-Type: application/json`

**Request Body:**
```json
{
  "companyName": "Công ty TNHH ABC",
  "taxCode": "0*********",
  "address": "123 Đường ABC, Quận 1, TP.HCM",
  "phoneNumber": "**********",
  "email": "<EMAIL>",
  "website": "https://abc.com",
  "businessLicenseUrl": "https://s3.amazonaws.com/bucket/business-license.pdf"
}
```

**Body Parameters:**
- `companyName` (string, required): Tên công ty
- `taxCode` (string, required): Mã số thuế
- `address` (string, required): Địa chỉ công ty
- `phoneNumber` (string, required): Số điện thoại
- `email` (string, required): Email công ty
- `website` (string, optional): Website công ty
- `businessLicenseUrl` (string, required): URL giấy phép kinh doanh

**Response 200:**
```json
{
  "success": true,
  "message": "Gửi thông tin doanh nghiệp thành công",
  "data": {
    "state": "BUSINESS_INFO_SUBMITTED",
    "nextActions": ["SUBMIT_FOR_APPROVAL"]
  }
}
```

**Error Responses:**
- `400`: Bad Request - Dữ liệu không hợp lệ hoặc trạng thái không cho phép
- `401`: Unauthorized
- `500`: Internal Server Error

---

## Error Codes

### Affiliate User Error Codes (11000-11099)

#### Tài Khoản Affiliate (11000-11009)
- `11000`: Không tìm thấy tài khoản affiliate
- `11001`: Tài khoản affiliate chưa được kích hoạt
- `11002`: Lỗi khi cập nhật trạng thái tài khoản affiliate

#### Rút Tiền (11010-11019)
- `11010`: Không tìm thấy yêu cầu rút tiền
- `11011`: Số dư không đủ để rút tiền
- `11012`: Số tiền rút không hợp lệ (tối thiểu 100,000 VND)
- `11013`: Lỗi khi tạo yêu cầu rút tiền
- `11014`: Lỗi khi cập nhật trạng thái rút tiền

#### Chuyển Đổi Điểm (11050-11059)
- `11050`: Không tìm thấy lịch sử chuyển đổi điểm
- `11051`: Số điểm không đủ để chuyển đổi
- `11052`: Số điểm chuyển đổi không hợp lệ (tối thiểu 100 điểm)
- `11053`: Lỗi khi tạo yêu cầu chuyển đổi điểm

#### Thống Kê và Truy Vấn (11040-11049)
- `11040`: Lỗi khi lấy thông tin thống kê affiliate
- `11041`: Lỗi khi lấy danh sách đơn hàng affiliate
- `11042`: Lỗi khi lấy danh sách khách hàng affiliate

#### Upload và Doanh Nghiệp (11060-11069)
- `11060`: Lỗi khi tạo URL upload
- `11061`: Loại file không được hỗ trợ
- `11062`: Kích thước file quá lớn
- `11063`: Thông tin doanh nghiệp đã tồn tại
- `11064`: Không tìm thấy thông tin doanh nghiệp

#### State Machine (11070-11079)
- `11070`: Trạng thái đăng ký không hợp lệ
- `11071`: Hành động không được phép ở trạng thái hiện tại
- `11072`: Dữ liệu đăng ký không đầy đủ
- `11073`: Tài khoản affiliate đã tồn tại

---

## Validation Rules

### Rút Tiền
- Số tiền tối thiểu: 100,000 VND
- Số tài khoản ngân hàng: 6-20 ký tự số
- Tên ngân hàng: Bắt buộc, tối đa 100 ký tự
- Tên chủ tài khoản: Bắt buộc, tối đa 100 ký tự

### Chuyển Đổi Điểm
- Số điểm tối thiểu: 100 điểm
- Tỷ lệ chuyển đổi: 1 điểm = 1,000 VND

### Thông Tin Doanh Nghiệp
- Tên công ty: Bắt buộc, tối đa 200 ký tự
- Mã số thuế: 10-13 ký tự số
- Địa chỉ: Bắt buộc, tối đa 500 ký tự
- Số điện thoại: 10-11 ký tự số
- Email: Định dạng email hợp lệ

### Upload File
- Loại file được hỗ trợ: PDF, JPEG, PNG
- Kích thước tối đa: 10MB
- Tên file: Tối đa 255 ký tự

### Phân Trang
- Page: >= 1
- Limit: 1-100
- Sort direction: ASC hoặc DESC

---

## Rate Limiting

- **Rút tiền**: Tối đa 5 yêu cầu/ngày
- **Chuyển đổi điểm**: Tối đa 10 yêu cầu/ngày
- **Upload file**: Tối đa 20 yêu cầu/giờ
- **APIs khác**: Tối đa 1000 yêu cầu/giờ
