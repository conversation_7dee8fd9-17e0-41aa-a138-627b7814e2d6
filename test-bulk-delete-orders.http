### Test API xóa nhiều đơn hàng

# 1. <PERSON><PERSON><PERSON> nhập để lấy token (thay đổi thông tin đăng nhập phù hợp)
POST http://localhost:3000/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}

### 

# 2. Xóa nhiều đơn hàng (thay YOUR_JWT_TOKEN bằng token thực tế)
DELETE http://localhost:3000/user/orders/bulk
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN

{
  "orderIds": [1, 2, 3]
}

### 

# 3. Test với danh sách rỗng (sẽ trả về lỗi validation)
DELETE http://localhost:3000/user/orders/bulk
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN

{
  "orderIds": []
}

### 

# 4. Test với ID không tồn tại
DELETE http://localhost:3000/user/orders/bulk
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN

{
  "orderIds": [999999, 999998]
}

### 

# 5. Test với mix ID tồn tại và không tồn tại
DELETE http://localhost:3000/user/orders/bulk
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN

{
  "orderIds": [1, 999999, 2, 999998]
}
